import {
  createScanRequests,
  updateScanRequest
} from '@modules/communities/repositories/scanRequest'
import * as scanRequestService from '@modules/communities/services/scanRequestUtils'
import { ScanRequestModel } from '@modules/communities/models/scanRequest'
describe('Scan Request Repository', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })
  describe('#createScanRequests', () => {
    it('Should add job id when creating scan requests', async () => {
      jest
        .spyOn(ScanRequestModel, 'insertMany')
        .mockResolvedValueOnce({} as any)
      const scanRequests = [
        {
          community: {
            _id: 'communityId'
          },
          scheduledFor: new Date()
        }
      ] as any
      const generateJobIdSpy = jest.spyOn(scanRequestService, 'generateJobId')
      await createScanRequests(scanRequests)
      expect(generateJobIdSpy).toHaveBeenCalledTimes(1)
      expect(generateJobIdSpy).toHaveBeenCalledWith(
        'communityId',
        scanRequests[0].scheduledFor
      )
      expect(ScanRequestModel.insertMany).toHaveBeenCalledTimes(1)
      expect(ScanRequestModel.insertMany).toHaveBeenCalledWith(scanRequests, {})
    })
  })

  describe('#updateScanRequest', () => {
    it('Should update scan request and add job id when scheduledFor is present', async () => {
      const query = { _id: 'scanRequestId' }
      const update = {
        scheduledFor: new Date()
      } as any

      jest
        .spyOn(ScanRequestModel, 'findOne')
        .mockResolvedValueOnce({ community: { _id: 'communityId' } } as any)

      const mockFindOneAndUpdate = {
        lean: jest.fn().mockResolvedValueOnce({})
      }
      const spyOnFindOneAndUpdate = jest
        .spyOn(ScanRequestModel, 'findOneAndUpdate')
        .mockReturnValueOnce(mockFindOneAndUpdate as any)

      const generateJobIdSpy = jest.spyOn(scanRequestService, 'generateJobId')

      await updateScanRequest(query, update)

      expect(generateJobIdSpy).toHaveBeenCalledTimes(1)
      expect(generateJobIdSpy).toHaveBeenCalledWith(
        'communityId',
        update.scheduledFor
      )
      expect(spyOnFindOneAndUpdate).toHaveBeenCalledTimes(1)
      expect(spyOnFindOneAndUpdate).toHaveBeenCalledWith(
        query,
        { $set: update },
        { new: true }
      )
      expect(mockFindOneAndUpdate.lean).toHaveBeenCalledTimes(1)
    })
  })
})
