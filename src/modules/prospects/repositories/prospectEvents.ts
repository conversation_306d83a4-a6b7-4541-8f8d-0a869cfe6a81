import { ProspectEvent } from '../types/prospectEvent'
import { Query } from '../types/prospect'
import { ProspectEventsModel } from '../models/prospectEvent'

export interface ProspectEventsQuery {
  prospectId: string
  externalId: string
}

export const findProspectEvents = async (
  query: ProspectEventsQuery
): Promise<ProspectEvent[]> => {
  const { prospectId, externalId } = query

  const mongoQuery: Query = {
    prospectId: prospectId,
    externalId: externalId
  }

  return ProspectEventsModel.find(mongoQuery).sort({ occurredAt: -1 }).limit(5)
}

export const countProspectEvents = async (
  query: ProspectEventsQuery
): Promise<number> => {
  const { prospectId, externalId } = query

  const mongoQuery: Query = {
    prospectId: prospectId,
    externalId: externalId
  }

  return ProspectEventsModel.countDocuments(mongoQuery).lean()
}
