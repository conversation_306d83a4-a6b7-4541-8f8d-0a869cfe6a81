import { ObjectId } from 'mongoose'
import { Address } from './address'

export enum DXSettingServices {
  ENTRATA = 'entrata',
  FUNNEL = 'funnel',
  APARTMENTS_DOT_COM = 'apartmentsDotCom',
  REALPAGE = 'realpage',
  YARDI = 'yardi',
  ANYONE_HOME = 'anyoneHome',
  KNOCK = 'knock',
  GS_DYNAMICS = 'gsDynamics',
  ENGRAIN = 'engrain',
  RENT_CAFE = 'rentCafe',
  SMART_RENT = 'smartRent',
  BUTTERFLY_MX = 'butterflyMX',
  RENT_CAFE_V2 = 'rentCafeV2',
  GEOKEY = 'geokey',
  LATCH = 'latch',
  ZILLOW = 'zillow',
  BOZZUTO_MITS = 'bozzutoMits',
  CORTLAND = 'cortland',
  SEAM = 'seam',
  RESMAN = 'resman'
}

export enum DXSettingServicesV2 {
  ELISE_AI = 'eliseAi',
  RESMAN = 'resman',
  BOZZUTO_MITS = 'bozzutoMits'
}

export interface ZillowSetting {
  address: Address
}

export interface PiSummariesSetting {
  store: boolean
  send: boolean
}

export interface EntrataSetting {
  subdomain: string
  user: string
  pass: string
  propertyId: number
  leadSourceId: number
  leasingAgentId?: number
  addressSeparators?: string[]
}

export interface YardiSettingAgentName {
  firstName?: string
  lastName?: string
}

export interface YardiSetting {
  url?: string
  entity?: string
  license?: string
  user: string
  pass: string
  propertyId: string
  serverName: string
  database: string
  agentName?: YardiSettingAgentName
  transactionSource?: string
}

export interface RealPageSetting {
  user: string
  pass: string
  siteId: number
  pmcId?: number
  addressSeparators: string[]
  useFloorPlanNameMarketing: boolean
}

export interface FunnelSetting {
  customerApiKey: string
  partnerApiKey: string
}

export interface AnyOneHomeSetting {
  propertyId: string
  apiKey: string
  listingContactEmail: string
  agentId?: string
}

export interface RentCafeSetting {
  apiToken: string
  propertyId: string
  url: string
}

export interface RentCafeV2Setting {
  apiToken: string
  propertyCode: string
  propertyId: number
  companyCode: string
  timezone: string
  url?: string
  marketingUrl?: string
  sendVirtualTours?: boolean
  source?: string
}

export interface GsDynamicsSetting {
  apiKey: string
  leadSource: string
  timezone: string
  url: string
}

export interface EngrainSetting {
  apiKey: string
  sightMapUrl: string
  unitMapUrl: string
  assetId: string
  service: DXSettingServices
  label: string
  name: string
  iconType: string
  sgtUrlsSent: boolean
  sendSgtUrls: boolean
  sendFloorplanTours: boolean
}

export interface DataExchangeSetting {
  _id?: string
  syncProspect?: boolean
  syncSgt?: boolean
  syncIls?: boolean
  syncScheduleATour?: boolean
  syncPms?: boolean
  syncPIData?: boolean
  syncProspectStatus?: boolean
  syncPiSummaries?: {
    store: boolean
    send: boolean
  }
  syncLease?: boolean
}

export interface DXSetting {
  _id: string | ObjectId
  communityId: string | ObjectId
  service: string
  funnel?: FunnelSetting
  entrata?: EntrataSetting
  realPage?: RealPageSetting
  anyoneHome?: AnyOneHomeSetting
  yardi?: YardiSetting
  knock?: KnockSetting
  rentCafe?: RentCafeSetting
  rentCafeV2?: RentCafeV2Setting
  gsDynamics?: GsDynamicsSetting
  engrain?: EngrainSetting
  dataExchange?: DataExchangeSetting
  smartRent?: SmartRentSetting
  butterflyMX?: ButterflyMXSetting
  geokey?: GeokeySetting
  latch?: LatchSetting
  reviewProspect?: boolean
  zillow?: ZillowSetting
  bozzutoMits?: BozzutoMitsSetting
  cortland?: CortlandSetting
  seam?: SeamSetting
  resman?: ResmanSetting
  createdAt?: Date
  updatedAt?: Date
  deletedAt?: Date
  updatedBy?: string
  buildingVersion?: string | undefined
  __v?: number
}

export interface BozzutoMitsSetting {
  propertyId: string
}

export interface CortlandSetting {
  propertyId: number
}

export interface SeamSetting {
  connectedAccountIds: string[]
}

export interface SmartRentSetting {
  propertyId: string
  apiKey: string
  apiSecret: string
}

export interface ButterflyMXSetting {
  buildings: number[]
}

export interface KnockSetting {
  propertyId: string
  version?: number
  companyId?: string
}

export interface GeokeySetting {
  propertyId: string
}

export interface LatchSetting {
  buildingUuid: string
}

export type PopulateDXSetting = Pick<
  DXSetting,
  '_id' | 'service' | 'dataExchange'
>

export interface ResmanSetting {
  propertyId: string
  useMarketingName: boolean
}
