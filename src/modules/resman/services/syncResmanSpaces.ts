import { logDebug, logError, logInfo, logWarn } from '@core/log'
import { findDXSettingById } from '@modules/communities/services/dxSetting'
import { createSpaceAsync } from '@modules/communities/services/space'
import { ExternalLinkService } from '@modules/communities/types/externalLink'
import { Space } from '@modules/communities/types/space'
import { SpaceType } from '@modules/communities/types/spaceType'
import {
  getProperties,
  getUnitAvailability,
  getUnitTypes
} from '@modules/resman/gateways/resman'
import {
  AvailabilityUnit,
  ResmanSpaceEventBody,
  UnitType
} from '@modules/resman/types/resman'
import { connectDb } from '@core/db'
import { Address } from '@modules/communities/types/address'

const location = 'sync-resman-spaces'

export const syncSpaces = async (dxSettingId: string) => {
  await connectDb()

  const dxSetting = await findDXSettingById(dxSettingId)
  if (!dxSetting) {
    logWarn(location, 'DX Setting not found', { dxSettingId })
    throw new Error(`DX Setting not found for id: ${dxSettingId}`)
  }
  const [propertiesData, unitsAvailabilityData, unitTypesData] =
    await Promise.all([
      getProperties(dxSetting.resman.propertyId),
      getUnitAvailability(dxSetting.resman.propertyId),
      getUnitTypes(dxSetting.resman.propertyId)
    ])

  const property = propertiesData.properties[0]
  const propertyAddress: Address = {
    street: property.streetAddress,
    city: property.city,
    state: property.state,
    postalCode: property.zip
  }

  logInfo(location, 'Received unit availability and unit types data', {
    dxSettingId: dxSetting._id.toString(),
    unitsCount: unitsAvailabilityData?.units?.length,
    unitTypesCount: unitTypesData?.unitTypes?.length
  })

  if (!unitsAvailabilityData?.units) {
    logWarn(location, 'No units found', { unitsAvailabilityData })
    return
  }

  let count = 0
  for (const unit of unitsAvailabilityData.units) {
    const unitType = unitTypesData?.unitTypes?.find(
      (type) => type.unitTypeId === unit.unitTypeId
    )
    if (!unitType) {
      logWarn(location, 'Unit type not found', {
        unitTypeId: unit.unitTypeId,
        unit
      })
      continue
    }

    const eventBody: ResmanSpaceEventBody = {
      setting: {
        dxSettingId: dxSetting._id.toString()
      },
      community: {
        _id: dxSetting.communityId.toString()
      },
      space: buildSpace({
        unit,
        unitType,
        useMarketingName: dxSetting.resman.useMarketingName
      }),
      building: {
        name: unit.buildingName,
        address: propertyAddress
      },
      address: propertyAddress,
      external: {
        externalLinkService: ExternalLinkService.Resman,
        buildingExternalId: unit.buildingId,
        spaces: [
          {
            externalId: unit.unitId,
            externalName: 'unitId',
            service: dxSetting.service
          }
        ]
      }
    }

    count++

    await createSpaceAsync(eventBody as any)
    logInfo(
      location,
      `Sent space to spacex ${count}/${unitsAvailabilityData.units.length}`,
      { unit: eventBody.space.unit }
    )
  }
}

export const buildSpace = ({
  unit,
  unitType,
  useMarketingName
}: {
  unit: AvailabilityUnit
  unitType: UnitType
  useMarketingName?: boolean
}): Partial<Space> => {
  logDebug(location, 'Building space from unit data', {
    unitId: unit?.unitId || 'unknown',
    unitNumber: unit?.number || 'unknown',
    unitTypeId: unitType?.unitTypeId || 'unknown'
  })

  const availableDate =
    unit?.dateAvailable && !isNaN(new Date(unit.dateAvailable).getTime())
      ? new Date(unit.dateAvailable)
      : undefined

  const space: Partial<Space> = {
    availableDate,
    readyToRentDate: availableDate,
    type: SpaceType.Unit,
    unit: unit.number,
    bedrooms: convertToNumber(unitType.bedrooms) || 0,
    bathrooms: convertToNumber(unitType.bathrooms) || 0,
    rentPrices: [],
    amenities: unit?.amenities?.map((amenity) => amenity?.name || '') || [],
    floorPlan: {
      name:
        (useMarketingName ? unit.unitTypeMarketingName : unit.unitTypeName) ||
        '',
      externalId: unit.unitTypeId
    },
    availabilityStatus: unit.vacancyStatus,
    unitSize: unit.squareFeet,
    pricesMetadata: {
      marketRent: unit.marketRent,
      unitTypeMarketRent: unitType.marketRent,
      requiredDeposit: unitType.requiredDeposit
    },
    availableDatesMetadata: {
      ...(unit.dateAvailable && { dateAvailable: availableDate }),
      ...(unit.statusDate && { statusDate: new Date(unit.statusDate) })
    },
    availableStatusesMetadata: {
      vacancyStatus: unit.vacancyStatus,
      excludedFromOccupancy: unit.excludedFromOccupancy,
      status: unit.status,
      availableForOnlineMarketing: unit.availableForOnlineMarketing
    },
    isVisible: !!unit.status,
    isMarketable: unit.availableForOnlineMarketing
  }
  logDebug(location, 'Built space from unit data', {
    space,
    unitId: unit.unitId
  })
  return space
}

export const convertToNumber = (value: string | number, fallback = 0) => {
  return !isNaN(Number(value)) ? Number(value) : fallback
}
