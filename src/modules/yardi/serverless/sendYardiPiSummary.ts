import { connectDb } from '@core/db'
import { logInfo, logWarn } from '@core/log'
import { SQSEvent } from 'aws-lambda'
import { sendSummary } from '../services/yardi'
import { findProspectById } from '@modules/prospects/services/prospect'
import { findDXSettingById } from '@modules/communities/services/dxSetting'

const location = 'yardi/summary-consumer'

export const handler = async (event: SQSEvent) => {
  logInfo(location, 'Received event', event)
  await connectDb()

  const batchItemFailures = []

  for (const record of event.Records) {
    try {
      const body = JSON.parse(record.body)
      const message = JSON.parse(body.Message)

      const prospect = await findProspectById(message.prospectId)
      const dxSetting = await findDXSettingById(message.dxSettingId)
      const summary = message.summary

      let isVirtualTour = true
      if (message.isVirtualTour === false) {
        isVirtualTour = false
      }

      await sendSummary(prospect, dxSetting, summary, isVirtualTour, true)
    } catch (err) {
      logWarn(location, 'error processing summary', {
        errorMessage: err.message
      })
      batchItemFailures.push({ itemIdentifier: record.messageId })
    }
  }

  return { batchItemFailures }
}
