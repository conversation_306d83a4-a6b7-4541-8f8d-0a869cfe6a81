import { mongoModelAclHook } from '@core/auth'
import {
  AnyOneHomeSetting,
  ButterflyMXSetting,
  DataExchangeSetting,
  DXSetting,
  EngrainSetting,
  EntrataSetting,
  FunnelSetting,
  GeokeySetting,
  LatchSetting,
  KnockSetting,
  RealPageSetting,
  RentCafeSetting,
  RentCafeV2Setting,
  SmartRentSetting,
  YardiSetting,
  ZillowSetting,
  BozzutoMitsSetting,
  CortlandSetting,
  PiSummariesSetting,
  SeamSetting,
  ResmanSetting
} from '@modules/communities/types/dxSetting'
import { model, Schema } from 'mongoose'

export const entrataSettingSchema = new Schema<EntrataSetting>({
  subdomain: {
    type: String,
    required: true
  },
  user: {
    type: String,
    required: true
  },
  pass: {
    type: String,
    required: true
  },
  leadSourceId: {
    type: Number,
    required: true
  },
  propertyId: {
    type: Number,
    required: true
  },
  leasingAgentId: {
    type: Number
  },
  addressSeparators: {
    type: [String]
  }
})

export const realPageSettingSchema = new Schema<RealPageSetting>({
  user: {
    type: String,
    required: true
  },
  pass: {
    type: String,
    required: true
  },
  siteId: {
    type: Number,
    required: true
  },
  pmcId: {
    type: Number
  },
  addressSeparators: {
    type: [String],
    required: true
  },
  useFloorPlanNameMarketing: {
    type: Boolean,
    required: false
  }
})

export const yardiSettingSchema = new Schema<YardiSetting>({
  url: {
    type: String
  },
  entity: {
    type: String
  },
  license: {
    type: String
  },
  user: {
    type: String,
    required: true
  },
  pass: {
    type: String,
    required: true
  },
  propertyId: {
    type: String,
    required: true
  },
  serverName: {
    type: String,
    required: true
  },
  database: {
    type: String,
    required: true
  },
  agentName: {
    firstName: {
      type: String,
      required: false
    },
    lastName: {
      type: String,
      required: false
    }
  },
  transactionSource: {
    type: String,
    required: false
  }
})

export const funnelSettingSchema = new Schema<FunnelSetting>({
  customerApiKey: {
    type: String,
    required: false
  },
  partnerApiKey: {
    type: String,
    required: false
  }
})

export const anyOneHomeSettingSchema = new Schema<AnyOneHomeSetting>(
  {
    propertyId: {
      type: String,
      required: true
    },
    apiKey: {
      type: String,
      required: true
    },
    listingContactEmail: {
      type: String,
      required: true
    }
  },
  { _id: false }
)

export const rentCafeSettingSchema = new Schema<RentCafeSetting>({
  apiToken: {
    type: String,
    required: true
  },
  propertyId: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: true
  }
})

export const rentCafeV2SettingSchema = new Schema<RentCafeV2Setting>({
  apiToken: {
    type: String,
    required: true
  },
  propertyCode: {
    type: String,
    required: true
  },
  propertyId: {
    type: Number,
    required: true
  },
  companyCode: {
    type: String,
    required: true
  },
  timezone: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: false
  },
  marketingUrl: {
    type: String,
    required: false
  },
  sendVirtualTours: {
    type: Boolean
  },
  source: {
    type: String,
    required: false
  }
})

export const knockSettingSchema = new Schema<KnockSetting>(
  {
    propertyId: {
      type: String,
      required: true
    },
    companyId: {
      type: String,
      required: false
    },
    version: {
      type: Number,
      required: false
    }
  },
  { _id: false }
)

export const gsDynamicsSettingSchema = new Schema({
  apiKey: {
    type: String,
    required: true
  },
  leadSource: {
    type: String,
    required: true
  },
  timezone: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: true
  }
})

export const engrainSettingSchema = new Schema<EngrainSetting>({
  service: {
    type: String,
    required: true
  },
  apiKey: {
    type: String,
    required: true
  },
  sightMapUrl: {
    type: String,
    required: true
  },
  unitMapUrl: {
    type: String,
    required: true
  },
  assetId: {
    type: String,
    required: true
  },
  label: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  iconType: {
    type: String,
    required: true
  },
  sgtUrlsSent: {
    type: Boolean,
    default: false
  },
  sendSgtUrls: {
    type: Boolean,
    default: false
  },
  sendFloorplanTours: {
    type: Boolean,
    default: false
  }
})

export const smartRentSettingSchema = new Schema<SmartRentSetting>({
  propertyId: {
    type: String,
    required: true
  }
})

export const butterflyRentSettingSchema = new Schema<ButterflyMXSetting>({
  buildings: [
    {
      type: Number,
      required: true
    }
  ]
})

export const geokeySettingSchema = new Schema<GeokeySetting>({
  propertyId: {
    type: String,
    required: true
  }
})

export const latchSettingSchema = new Schema<LatchSetting>({
  buildingUuid: {
    type: String,
    required: true
  }
})

export const bozzutoMitsSettingSchema = new Schema<BozzutoMitsSetting>({
  propertyId: {
    type: String,
    required: true
  }
})

export const cortlandSettingSchema = new Schema<CortlandSetting>({
  propertyId: {
    type: Number,
    required: true
  }
})

export const seamSettingSchema = new Schema<SeamSetting>({
  connectedAccountIds: {
    type: [String],
    required: true
  }
})

const piSummariesSettingSchema = new Schema<PiSummariesSetting>({
  store: {
    type: Boolean,
    required: true,
    default: false
  },
  send: {
    type: Boolean,
    required: true,
    default: false
  }
})

export const dataExchangeSettingSchema = new Schema<DataExchangeSetting>({
  syncProspect: {
    type: Boolean,
    required: true,
    default: false
  },
  syncSgt: {
    type: Boolean,
    required: true,
    default: false
  },
  syncIls: {
    type: Boolean,
    required: true,
    default: false
  },
  syncScheduleATour: {
    type: Boolean,
    required: true,
    default: false
  },
  syncPms: {
    type: Boolean,
    required: true,
    default: false
  },
  syncPIData: {
    type: Boolean,
    required: true,
    default: false
  },
  syncProspectStatus: {
    type: Boolean,
    required: true,
    default: false
  },
  syncPiSummaries: { type: piSummariesSettingSchema },
  syncLease: {
    type: Boolean,
    required: true,
    default: false
  }
})

export const zillowSettingSchema = new Schema<ZillowSetting>({
  address: {
    street: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    state: {
      type: String,
      required: true
    },
    postalCode: {
      type: String,
      required: true
    },
    country: {
      type: String,
      required: false
    }
  }
})

const resmanSettingSchema = new Schema<ResmanSetting>({
  propertyId: {
    type: String,
    required: true
  },
  useMarketingName: {
    type: Boolean,
    default: false
  }
})

export const dxSettingSchema = new Schema<DXSetting>(
  {
    communityId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'Community'
    },
    service: {
      type: String,
      required: true
    },
    funnel: {
      type: funnelSettingSchema
    },
    entrata: {
      type: entrataSettingSchema
    },
    realPage: {
      type: realPageSettingSchema
    },
    yardi: {
      type: yardiSettingSchema
    },
    anyoneHome: {
      type: anyOneHomeSettingSchema
    },
    deletedAt: { type: Date, default: null },
    knock: {
      type: knockSettingSchema
    },
    rentCafe: {
      type: rentCafeSettingSchema
    },
    rentCafeV2: {
      type: rentCafeV2SettingSchema
    },
    gsDynamics: {
      type: gsDynamicsSettingSchema
    },
    engrain: {
      type: engrainSettingSchema
    },
    dataExchange: {
      type: dataExchangeSettingSchema
    },
    smartRent: {
      type: smartRentSettingSchema
    },
    butterflyMX: {
      type: butterflyRentSettingSchema
    },
    geokey: {
      type: geokeySettingSchema
    },
    latch: {
      type: latchSettingSchema
    },
    reviewProspect: {
      type: Boolean,
      required: false
    },
    zillow: {
      type: zillowSettingSchema
    },
    updatedBy: { type: String, default: null },
    buildingVersion: { type: String, required: false },
    bozzutoMits: {
      type: bozzutoMitsSettingSchema
    },
    cortland: {
      type: cortlandSettingSchema
    },
    seam: {
      type: seamSettingSchema
    },
    resman: {
      type: resmanSettingSchema
    }
  },
  { timestamps: true }
)

dxSettingSchema.index(
  { communityId: 1, service: 1, deletedAt: 1 },
  { unique: true }
)

dxSettingSchema.pre('find', mongoModelAclHook)
dxSettingSchema.pre('findOne', mongoModelAclHook)
dxSettingSchema.pre('countDocuments', mongoModelAclHook)

export const DXSettingModel = model<DXSetting>('dxSetting', dxSettingSchema)
