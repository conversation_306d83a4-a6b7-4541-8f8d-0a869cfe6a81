import { Address } from '@modules/communities/types/address'
import { Space } from '@modules/communities/types/space'
import { ExternalLinkService } from '@modules/communities/types/externalLink'

export interface UnitAvailabilityData {
  units: AvailabilityUnit[]
  propertyId: string
  accountId: number
}

export interface AvailabilityUnit {
  propertyId: string
  unitId: string
  number: string
  floor: string
  squareFeet: number
  unitTypeId: string
  unitTypeName: string
  unitTypeMarketingName: string | null
  availableForOnlineMarketing: boolean
  buildingId: string
  buildingName: string
  marketRent: number | null
  streetAddress: string | null
  city: string | null
  state: string | null
  zip: string | null
  status: string
  statusDate: string
  dateAvailable: string | null
  vacancyStatus: string
  excludedFromOccupancy: boolean
  amenities: Amenity[]
  occupyingLease: OccupyingLease | null
  applicantLease: ApplicantLease | null
}

interface Amenity {
  amenityId: string
  name: string
  charge: number
  impactsMarketRent: boolean
  availableForOnlineMarketing: boolean
}

interface OccupyingLease {
  billingAccountId: string
  leaseId: string
  moveInDate: string
  noticeToVacateDate: string | null
  moveOutDate: string | null
}

interface ApplicantLease {
  billingAccountId: string
  leaseId: string
  applicationDate: string
  moveInDate: string
}

export interface UnitTypesData {
  unitTypes: UnitType[]
  propertyId: string
  accountId: number
}

export interface UnitType {
  unitTypeId: string
  propertyId: string
  name: string
  description: string
  bedrooms: number
  bathrooms: number
  squareFootage: number
  maximumOccupancy: number
  marketRent: number
  requiredDeposit: number
}

export interface PropertiesData {
  properties: Property[]
  accountId: number
}

export interface Property {
  propertyId: string
  abbreviation: string
  name: string
  type: string
  streetAddress: string
  city: string
  state: string
  zip: string
  phone: string
  email: string
  timeZone: string
  manager: string
  currentPeriod: CurrentPeriod
  managementTeam: ManagementTeamMember[]
}

interface CurrentPeriod {
  start: string
  end: string
}

interface ManagementTeamMember {
  personId: string
  firstName: string
  lastName: string
  role: string
}

export interface ResmanBuilding {
  name: string
  address: Address
}

export interface ResmanSpaceEventBody {
  setting: {
    dxSettingId: string
  }
  community: {
    _id: string
  }
  space: Partial<Space>
  building?: ResmanBuilding
  address: Address
  external: {
    default?: string
    spaces: {
      externalName?: string
      externalId: string
      service: string
    }[]
    buildingExternalId?: string
    externalLinkService: ExternalLinkService
  }
}

export class ResmanError extends Error {
  status: number
  payload: any
  propertyId?: string
  endpoint?: string
  originalError?: any

  constructor(
    message: string,
    propertyId?: string,
    endpoint?: string,
    originalError?: any,
    payload: any = {}
  ) {
    super(message)
    Error.captureStackTrace(this, this.constructor)

    this.name = this.constructor.name
    this.status = 500
    this.propertyId = propertyId
    this.endpoint = endpoint
    this.originalError = originalError
    this.payload = {
      ...payload,
      propertyId,
      endpoint,
      originalError: originalError?.message || originalError
    }
  }
}
