import { getAuthUser } from '@core/auth'
import BadRequestError from '@core/errors/badRequestError'
import NotFoundError from '@core/errors/notFoundError'
import { logDebug, logError, logInfo, logWarn } from '@core/log'
import { buildCalendarLinks, KLAVIYO_EVENTS, sendMail } from '@core/mail'
import { PaginatedResult } from '@core/types'
import { findRolesByQuery } from '@modules/users/repositories/role'
import { findUsers } from '@modules/users/repositories/user'
import { RoleAliases } from '@modules/users/types/role'
import { User, UserTag } from '@modules/users/types/user'
import { format, isDate, isSameDay, isValid } from 'date-fns'
import {
  findCommunityById,
  findCommunityByIdLean
} from '../repositories/community'
import * as scanRequestRepo from '../repositories/scanRequest'
import { findScanRequestsByQuery } from '../repositories/scanRequest'
import { findSpacesByQuery } from '../repositories/space'
import { Community, planFeatures } from '../types/community'
import {
  CreateScanRequestsDTO,
  DateField,
  FindScanRequestsCountByCommunitiesDTO,
  FindScanRequestsDTO,
  GroupScanRequest,
  GroupScanRequestParams,
  ScanRequest,
  ScanRequestStatus
} from '../types/scanRequest'
import { findSpaceById } from './space'
import { Space } from '../types/space'
import { getEnvVariable } from '@core/util'
import { findUserByEmail, findUserById } from '@modules/users/services/user'
import { ObjectId } from '../types/id'

const location = 'src/modules/communities/services/scanRequest.ts'

type FormattedScanRequest = {
  order: number
  unit: string
  floorPlan: string
  reason?: string
}

type GetListResponse = {
  scansList: FormattedScanRequest[]
  updatedList: FormattedScanRequest[]
}

type ScanRequestStatusList = {
  forList: ScanRequestStatus
  forUpdate: ScanRequestStatus
}

type EmailParams = {
  communityName: string
  scansList: FormattedScanRequest[]
  scansListLength: number
  updatedList: FormattedScanRequest[]
  updatedListLength: number
  scanRequestsDashboardURL: string
  dateString: string
  userName: string
  appleCalendarLink: string
  googleCalendarLink: string
  outlookCalendarLink: string
}

type RescheduledEmailParams = EmailParams & {
  originalScheduledForDate: string
  newScheduledForDate: string
  scannerArrivalWindow: string
}

type ScannerEmailParams = {
  userName: string
  scannerEmail: string
  scheduledFor: string
  scannerArrivalTime: {
    from: string
    to: string
  }
  community: {
    name: string
    address: string
  }
  scansList: Array<{
    order: number
    unit: string
    floorPlan: string
    building: {
      name: string
      address: string
    }
  }>
}

export const createScanRequests = async (
  createScanRequests: CreateScanRequestsDTO
): Promise<ScanRequest[]> => {
  logInfo(location, 'Creating scan requests.', createScanRequests)
  const requestedAt = new Date()

  const community: Pick<
    Community,
    '_id' | 'name' | 'organization' | 'metroArea' | 'features'
  > = await findCommunityById(
    createScanRequests.community._id.toString(),
    'organization name _id metroArea features'
  )
  if (!community) throw new NotFoundError('Community not found.')
  const user = getAuthUser()

  let isScheduled = false
  let scheduledAt: Date | null = null
  if (
    createScanRequests.status === ScanRequestStatus.SCHEDULED ||
    isValid(new Date(createScanRequests.scheduledFor))
  ) {
    isScheduled = true
    scheduledAt = new Date()
  }

  const currentScheduledForStart = isValid(
    new Date(createScanRequests.scheduledFor)
  )
    ? new Date(createScanRequests.scheduledFor)
    : new Date()

  currentScheduledForStart.setHours(0, 0, 0, 0)
  const currentScheduledForEnd = new Date(currentScheduledForStart)
  currentScheduledForEnd.setHours(23, 59, 59, 999)

  const now = new Date()
  now.setHours(0, 0, 0, 0)

  if (currentScheduledForStart < now) {
    throw new BadRequestError('Scheduled for date cannot be in the past.')
  }

  const existingCompletedWithSameDate = (
    await Promise.all(
      createScanRequests.spaces.map((space) =>
        scanRequestRepo.findScanRequestByQuery({
          'space._id': space._id.toString(),
          status: ScanRequestStatus.COMPLETED,
          scheduledFor: {
            $gte: currentScheduledForStart,
            $lt: currentScheduledForEnd
          }
        })
      )
    )
  ).filter((scanRequest) => scanRequest)

  const existingRequestedOrScheduled =
    await scanRequestRepo.findScanRequestsByQuery({
      'space._id': {
        $in: createScanRequests.spaces.map((space) => space._id.toString())
      },
      status: {
        $in: [ScanRequestStatus.REQUESTED, ScanRequestStatus.SCHEDULED]
      }
    })

  const existingScanRequestsForSpaces = [
    ...existingCompletedWithSameDate,
    ...existingRequestedOrScheduled
  ]

  if (existingScanRequestsForSpaces?.length > 0) {
    createScanRequests.spaces = createScanRequests.spaces.filter(
      (space) =>
        !existingScanRequestsForSpaces.some(
          (scanRequest) =>
            scanRequest.space._id.toString() === space._id.toString()
        )
    )
  }

  if (createScanRequests?.spaces?.length <= 0) {
    return existingScanRequestsForSpaces
  }

  const spacesRequests = []
  for (const space of createScanRequests.spaces) {
    spacesRequests.push(
      findSpace(
        space._id.toString(),
        '_id type unit building bedrooms bathrooms floorPlan availabilityStatus unitSize isComplete token tourLastUpdatedDate'
      )
    )
  }
  const spaces: Space[] = await Promise.all(spacesRequests)

  let status = createScanRequests.status
  if (!status) {
    status = createScanRequests.scheduledFor
      ? ScanRequestStatus.SCHEDULED
      : ScanRequestStatus.REQUESTED
  }

  const scanRequest = createScanRequests.spaces.map((_, i) => {
    const spaceAvailabilityWhenRequested =
      spaces[i].availabilityStatus || undefined
    const spaceAvailabilityWhenScheduled = isScheduled
      ? spaces[i].availabilityStatus
      : undefined

    return {
      ...createScanRequests,
      community,
      requestedBy: {
        _id: user._id,
        name: user.name,
        email: user.email
      },
      requestedAt,
      space: spaces[i],
      status,
      isAutoScan:
        user.roleAlias === RoleAliases?.ADMIN &&
        community?.features?.includes(planFeatures.AutoScan),
      ...(scheduledAt && { scheduledAt }),
      ...(spaceAvailabilityWhenRequested && { spaceAvailabilityWhenRequested }),
      ...(spaceAvailabilityWhenScheduled && { spaceAvailabilityWhenScheduled }),
      ...(isScheduled && { scheduledFor: currentScheduledForStart }),
      isRescan: spaces[i].isComplete,
      parrotsRequest: isRequestedByAdmin(scanRequest)
    }
  })

  const result = (await scanRequestRepo.createScanRequests(
    scanRequest
  )) as ScanRequest[]

  result.push(...existingScanRequestsForSpaces)

  logInfo(location, 'Created scan requests.', result)

  return result
}

export const findScanRequests = async (
  query: FindScanRequestsDTO
): Promise<PaginatedResult<ScanRequest | GroupScanRequest>> => {
  if (query.startDate) {
    query.dateField = query.dateField || DateField.ScannedAt
    query.startDate = new Date(query.startDate)
  }

  if (query.endDate) {
    query.dateField = query.dateField || DateField.ScannedAt
    query.endDate = query.endDate ? new Date(query.endDate) : new Date()
  }

  if (query.spaceId || query['space._id']) {
    query['space._id'] = new ObjectId(query.spaceId || query['space._id'])
    delete query.spaceId
  }

  const result = await scanRequestRepo.findScanRequests(query)
  const user = getAuthUser()
  if (user.roleAlias !== RoleAliases.ADMIN) {
    result.data = result.data.map((scanRequest) => ({
      ...scanRequest,
      requestedBy: maskRequestedBy(scanRequest)
    }))
  }
  if (query.orderBy === 'scheduledFor') {
    result.data.forEach((scanRequest, i) => {
      if (!scanRequest.scheduledFor) {
        result.data.splice(i, 1)
        if (query.order === 'asc' || !query.order) {
          result.data.push(scanRequest)
        } else {
          result.data.unshift(scanRequest)
        }
      }
    })
  }

  return result
}

export const updatedScanRequestById = async (
  _id: string,
  updateScanRequest: Partial<ScanRequest>
): Promise<ScanRequest> => {
  let result: ScanRequest
  switch (updateScanRequest.status) {
    case ScanRequestStatus.COMPLETED:
      result = await completeScanRequest(_id)
      break
    case ScanRequestStatus.CANCELED:
      result = await cancelScanRequest(_id, updateScanRequest.reason)
      break
    case ScanRequestStatus.SKIPPED:
      result = await skipScanRequest(
        _id,
        updateScanRequest.reason,
        null,
        updateScanRequest.comment
      )
      break
    case ScanRequestStatus.SCHEDULED:
      result = await scheduleScanRequest(
        _id,
        updateScanRequest.scheduledFor,
        updateScanRequest.scannerArrivalWindow,
        updateScanRequest.scannerEmail
      )
      break
    default:
      result = await scanRequestRepo.updateScanRequest(
        { _id },
        updateScanRequest
      )
      break
  }

  return result
}

export const updateAllScanRequestByCommunityId = async (
  communityId: string,
  updateData: Partial<ScanRequest>
): Promise<number> => {
  return scanRequestRepo.updateAllScanRequestByCommunityId(
    communityId,
    updateData
  )
}

export const updateScanRequests = async (
  scanRequests: Partial<ScanRequest>[]
) => {
  const canceledScanRequests = scanRequests.filter(
    (scanRequest) => scanRequest.status === ScanRequestStatus.CANCELED
  )

  let canceledScanRequestsBeforeUpdate: ScanRequest[] = []
  if (canceledScanRequests?.length) {
    canceledScanRequestsBeforeUpdate = await findScanRequestsByQuery({
      _id: { $in: canceledScanRequests.map((sr) => sr._id.toString()) }
    })
  }

  const result = await Promise.all(
    scanRequests.map((scanRequest) =>
      updatedScanRequestById(scanRequest._id.toString(), scanRequest)
    )
  )

  if (canceledScanRequestsBeforeUpdate?.length) {
    await sendCanceledScanRequestEmails(canceledScanRequestsBeforeUpdate)
  }

  return result
}

const sendCanceledScanRequestEmails = async (
  scanRequests: ScanRequest[]
): Promise<void> => {
  if (!scanRequests?.length) return

  const requests = scanRequests.filter(
    (sr) => sr.status === ScanRequestStatus.SCHEDULED
  )

  if (requests.length === 0) {
    logDebug(
      location,
      'E-mail not sent because the scan request has not been scheduled yet.'
    )
    return
  }

  const recipients = await getRecipients(
    requests[0].community._id.toString(),
    [
      RoleAliases.AGENT,
      RoleAliases.ORG_ADMIN,
      RoleAliases.BASIC,
      RoleAliases.OWNER
    ],
    { receiveCanceledRequestsNotification: true }
  )
  logDebug(
    location,
    'Recipients found.',
    recipients.map((r) => r.email)
  )
  if (recipients?.length) {
    const scanRequestWithJobId = requests.find((sr: ScanRequest) =>
      getJobId(sr)
    )

    let data = {} as GetListResponse

    if (scanRequestWithJobId?.jobId || scanRequestWithJobId?.scheduledFor) {
      data = await getScanAndUpdateList(scanRequestWithJobId.jobId, {
        forList: ScanRequestStatus.SCHEDULED,
        forUpdate: ScanRequestStatus.CANCELED
      })
    } else {
      data.updatedList = formatScanRequests(requests)
    }

    const calendarLinks = getCalendar(requests[0])
    const formattedDateString = formatCanceledScanRequestDateString(requests[0])
    const promises = recipients.map((recipient) =>
      sendMail(recipient.email, KLAVIYO_EVENTS.SCAN_REQUESTS_CANCELED, {
        communityName: requests[0].community.name,
        scansList: data.scansList ?? [],
        updatedList: data.updatedList ?? [],
        scansListLength: data.scansList?.length,
        updatedListLength: data.updatedList?.length,
        scanRequestsDashboardURL: `${process.env.AGENT_DASHBOARD_WEB_URL}/scan-requests/add-new-request`,
        userName: recipient.name,
        dateString: formattedDateString,
        scheduledFor:
          requests[0]?.scheduledFor &&
          format(requests[0]?.scheduledFor, 'MM/dd/yyyy'),
        appleCalendarLink: calendarLinks?.apple,
        googleCalendarLink: calendarLinks?.google,
        outlookCalendarLink: calendarLinks?.outlook
      })
    )
    const promisesResult = await Promise.allSettled(promises)
    promisesResult.forEach((promise) => {
      if (promise.status === 'fulfilled') {
        logDebug(location, 'Email sent successfully.', {
          data: promise
        })
      } else {
        logWarn(location, `Error sending email ${promise.reason}.`)
      }
    })
  }
}

// Validation helpers
const validateScheduledRequests = (scheduledRequests: ScanRequest[]): void => {
  if (!scheduledRequests || scheduledRequests.length === 0) {
    throw new NotFoundError('Scan requests not found.')
  }

  // Validate skipped requests
  const skippedRequests = scheduledRequests.filter(
    (sr) => sr.status === ScanRequestStatus.SKIPPED
  )
  if (skippedRequests.length) {
    throw new BadRequestError(
      'Skipped scan requests with status cannot be rescheduled.'
    )
  }

  const firstRequest = scheduledRequests[0]
  if (!firstRequest.community?._id) {
    throw new BadRequestError(
      'Invalid scan request: missing community information.'
    )
  }

  if (!firstRequest.scheduledFor) {
    throw new BadRequestError('Invalid scan request: missing scheduled date.')
  }

  if (
    !firstRequest.scannerArrivalWindow?.from ||
    !firstRequest.scannerArrivalWindow?.to
  ) {
    throw new BadRequestError(
      'Invalid scan request: missing scanner arrival window.'
    )
  }
}

const validateUsers = (users: User[]): void => {
  if (!users || users.length === 0) {
    throw new NotFoundError(
      '[sendScheduledConfirmation] Users eligible to receive the email not found.'
    )
  }
}

// Email parameter builders
const buildBaseEmailParams = (
  scheduledRequest: ScanRequest,
  scansList: FormattedScanRequest[],
  updatedList: FormattedScanRequest[],
  userName: string,
  calendarLinks: { apple: string; google: string; outlook: string },
  formattedDateString: string
): EmailParams => {
  return {
    communityName: scheduledRequest.community.name,
    scansList,
    scansListLength: scansList.length,
    updatedList,
    updatedListLength: updatedList.length,
    scanRequestsDashboardURL: `${process.env.AGENT_DASHBOARD_WEB_URL}/scan-requests/upcoming-shoots`,
    dateString: formattedDateString,
    userName,
    appleCalendarLink: calendarLinks.apple,
    googleCalendarLink: calendarLinks.google,
    outlookCalendarLink: calendarLinks.outlook
  }
}

const buildScannerEmailParams = (
  scanner: User,
  scanRequests: ScanRequest[],
  community: Community,
  buildingAddress: string,
  communityAddress: string
): ScannerEmailParams => {
  return {
    userName: scanner.name,
    scannerEmail: scanRequests[0].scannerEmail,
    scheduledFor: new Intl.DateTimeFormat('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    }).format(scanRequests[0].scheduledFor),
    scannerArrivalTime: {
      from: scanRequests[0].scannerArrivalWindow.from,
      to: scanRequests[0].scannerArrivalWindow.to
    },
    community: {
      name: community.name,
      address: communityAddress
    },
    scansList: scanRequests.map((scanRequest, i) => ({
      order: i + 1,
      unit: scanRequest.space?.unit || '',
      floorPlan: scanRequest.space?.floorPlan?.name || '',
      building: {
        name: scanRequest.space?.building?.name || '',
        address: buildingAddress || ''
      }
    }))
  }
}

// Email sending helpers
const sendUserConfirmationEmail = async (
  user: User,
  klavyioTemplate: string,
  params: EmailParams
): Promise<void> => {
  try {
    await sendMail(user.email, klavyioTemplate, params)
    logDebug(location, `Successfully sent confirmation email to ${user.email}`)
  } catch (error) {
    logError(
      location,
      `Error sending confirmation email to ${user.email.substring(
        user.email.indexOf('@') + 1,
        user.email.length - 1
      )}...`,
      { error, userId: user._id, template: klavyioTemplate }
    )
  }
}

const sendRescheduledEmail = async (
  user: User,
  params: RescheduledEmailParams
): Promise<void> => {
  try {
    await sendMail(user.email, KLAVIYO_EVENTS.SCAN_REQUESTS_RESCHEDULED, params)
    logDebug(location, `Successfully sent rescheduled email to ${user.email}`)
  } catch (error) {
    logError(
      location,
      `Error sending rescheduled email to ${user.email.substring(
        user.email.indexOf('@') + 1,
        user.email.length - 1
      )}...`,
      { error, userId: user._id }
    )
  }
}

const sendScannerConfirmationEmail = async (
  scannerEmail: string,
  params: ScannerEmailParams
): Promise<void> => {
  try {
    await sendMail(
      scannerEmail,
      KLAVIYO_EVENTS.SCANNER_SCHEDULED_SCANS_CONFIRMATION,
      params
    )
    logInfo(
      location,
      `Sent scheduled confirmation email to scanner ${scannerEmail}.`
    )
  } catch (error) {
    logError(
      location,
      `Error sending scanner confirmation email to ${scannerEmail}`,
      { error, scannerEmail }
    )
  }
}

// Address building helper
const buildAddresses = (
  scanRequest: ScanRequest
): { building: string; community: string } => {
  let buildingAddress = ''
  let communityAddress = ''

  if (scanRequest.space?.building?.address) {
    const addr = scanRequest.space.building.address
    buildingAddress = `${addr.street}, ${addr.city}, ${addr.state}, ${addr.postalCode}`
    communityAddress = buildingAddress
  }

  if (communityAddress === '') {
    communityAddress = buildingAddress
  }

  return { building: buildingAddress, community: communityAddress }
}

// Rescheduled requests handler
const handleRescheduledRequests = async (
  rescheduledScanRequests: ScanRequest[],
  ids: string[],
  users: User[],
  baseParams: EmailParams
): Promise<void> => {
  if (!rescheduledScanRequests.length) return

  const previousJobId = `${rescheduledScanRequests[0].community._id.toString()}-${format(
    rescheduledScanRequests[0].previousScheduledForDate,
    'yyyyMMdd'
  )}`

  const stillScheduledScanRequests =
    await scanRequestRepo.findScanRequestsByQuery({
      _id: { $nin: ids.map((id: string) => new ObjectId(id)) },
      deletedAt: null,
      jobId: previousJobId,
      status: ScanRequestStatus.SCHEDULED
    })

  const updatedList = formatScanRequests(stillScheduledScanRequests)
  let rescheduledList = formatScanRequests(rescheduledScanRequests)

  rescheduledList = rescheduledList.filter(
    (r: FormattedScanRequest) =>
      !updatedList.some(
        (u: FormattedScanRequest) =>
          r.unit === u.unit && r.floorPlan === u.floorPlan
      )
  )

  const scannerArrivalWindow = `The scanner will arrive between ${rescheduledScanRequests[0]?.scannerArrivalWindow?.from} and ${rescheduledScanRequests[0]?.scannerArrivalWindow?.to}.`

  const rescheduledParams: RescheduledEmailParams = {
    ...baseParams,
    scansList: updatedList,
    scansListLength: updatedList.length,
    updatedList: rescheduledList,
    updatedListLength: rescheduledList.length,
    originalScheduledForDate: format(
      rescheduledScanRequests[0].previousScheduledForDate,
      'MM/dd/yyyy'
    ),
    newScheduledForDate: format(
      rescheduledScanRequests[0].scheduledFor,
      'MM/dd/yyyy'
    ),
    scannerArrivalWindow
  }

  await Promise.all(
    users.map((user) => sendRescheduledEmail(user, rescheduledParams))
  )
}

// Process existing scan requests
const processExistingScanRequests = async (
  jobId: string,
  scheduledRequests: ScanRequest[],
  ids: string[]
): Promise<{
  existingScanRequests: ScanRequest[]
  rescheduledScanRequests: ScanRequest[]
}> => {
  if (!jobId) {
    return { existingScanRequests: [], rescheduledScanRequests: [] }
  }

  const allExistingRequests = await scanRequestRepo.findScanRequestsByQuery({
    deletedAt: null,
    jobId: scheduledRequests[0].jobId,
    status: ScanRequestStatus.SCHEDULED
  })

  const rescheduledScanRequests = allExistingRequests.filter(
    (sr: ScanRequest) => sr.isRescheduled
  )

  const existingScanRequests = allExistingRequests.filter(
    (sr: ScanRequest) => !sr.isRescheduled && !ids.includes(sr._id.toString())
  )

  return { existingScanRequests, rescheduledScanRequests }
}

// Scanner email handler
const sendScannerEmails = async (
  scheduledRequests: ScanRequest[]
): Promise<void> => {
  logInfo(location, 'Sending scheduled confirmation emails to scanners.')

  const groupedScheduledRequests = scheduledRequests.reduce((acc, request) => {
    if (!request.scannerEmail) return acc
    if (!acc[request.scannerEmail]) acc[request.scannerEmail] = []
    acc[request.scannerEmail].push(request)
    return acc
  }, {} as { [key: string]: ScanRequest[] })

  const scannerEmailPromises = Object.entries(groupedScheduledRequests).map(
    async ([scannerEmail, scanRequests]) => {
      if (!scanRequests[0].scannerEmail) return

      try {
        const scanner = await findUserByEmail(scanRequests[0].scannerEmail)
        if (!scanner) {
          logWarn(location, `Scanner not found for email: ${scannerEmail}`)
          return
        }

        const community = await findCommunityByIdLean(
          scanRequests[0].community._id.toString()
        )
        if (!community) {
          logWarn(
            location,
            `Community not found for scanner email: ${scannerEmail}`
          )
          return
        }

        const addresses = buildAddresses(scanRequests[0])
        const scannerParams = buildScannerEmailParams(
          scanner,
          scanRequests,
          community,
          addresses.building,
          addresses.community
        )

        await sendScannerConfirmationEmail(scannerEmail, scannerParams)
      } catch (error) {
        logError(
          location,
          `Error processing scanner email for ${scannerEmail}`,
          { error, scannerEmail }
        )
      }
    }
  )

  await Promise.allSettled(scannerEmailPromises)
}

export const completeScanRequest = async (id: string, isUpload?: boolean) => {
  const scanRequest = await scanRequestRepo.findScanRequestById(id)
  if (!scanRequest) throw new NotFoundError('Scan request not found.')
  if (scanRequest.status === ScanRequestStatus.CANCELED) {
    logWarn(
      location,
      'Tried to COMPLETE a CANCELED scan request.',
      JSON.stringify(scanRequest)
    )
    return scanRequest
  }
  if (scanRequest.status === ScanRequestStatus.SKIPPED) {
    logWarn(
      location,
      'Tried to COMPLETE a SKIPPED scan request.',
      JSON.stringify(scanRequest)
    )
    return scanRequest
  }
  if (scanRequest.status === ScanRequestStatus.COMPLETED && !isUpload) {
    logInfo(
      location,
      'Tried to COMPLETE an already COMPLETED scan request.',
      JSON.stringify(scanRequest)
    )
    return scanRequest
  }

  let authUser = getAuthUser() as User
  if (!authUser) {
    const workLogJanitorId = getEnvVariable('WORKLOG_JANITOR_ID')
    authUser = await findUserById(workLogJanitorId)
  }
  const scannedBy = {
    _id: authUser._id,
    name: authUser.name,
    email: authUser.email
  }

  const spaceAvailabilityStatus = (
    await findSpaceById(scanRequest.space._id.toString(), 'availabilityStatus')
  )?.availabilityStatus

  return await scanRequestRepo.updateScanRequest(
    { _id: id },
    {
      status: ScanRequestStatus.COMPLETED,
      scannedAt: new Date(),
      scannedBy,
      ...(isUpload && { uploadedAt: new Date() }),
      ...(isUpload && {
        uploadedBy: {
          _id: authUser._id,
          name: authUser.name,
          email: authUser.email
        }
      }),
      ...(!scanRequest.scheduledFor && {
        scheduledFor: new Date()
      }),
      ...(!scanRequest.scheduledBy && {
        scheduledBy: {
          _id: authUser._id,
          name: authUser.name,
          email: authUser.email
        }
      }),
      ...(spaceAvailabilityStatus && {
        spaceAvailabilityWhenCompleted: spaceAvailabilityStatus
      }),
      ...(spaceAvailabilityStatus && {
        spaceAvailabilityWhenCompleted: spaceAvailabilityStatus
      })
    }
  )
}

export const sendScheduledConfirmation = async (ids: string[]) => {
  // Input validation
  if (!ids || ids.length === 0) {
    throw new BadRequestError('No scan request IDs provided.')
  }

  // Fetch and validate scheduled requests
  const scheduledRequests = await scanRequestRepo.findScanRequestsByQuery({
    _id: { $in: ids }
  })
  validateScheduledRequests(scheduledRequests)

  const firstRequest = scheduledRequests[0]
  const jobId = scheduledRequests.find((sr: ScanRequest) => getJobId(sr))?.jobId

  // Process existing scan requests
  const { existingScanRequests, rescheduledScanRequests } =
    await processExistingScanRequests(jobId, scheduledRequests, ids)

  // Get recipients
  const users = await getRecipients(
    firstRequest.community._id.toString(),
    [
      RoleAliases.AGENT,
      RoleAliases.ORG_ADMIN,
      RoleAliases.BASIC,
      RoleAliases.OWNER
    ],
    { receiveScanScheduledConfirmation: true }
  )
  validateUsers(users)

  // Format scan requests
  const updatedList = formatScanRequests(existingScanRequests)
  const scansList = formatScanRequests(scheduledRequests)

  // Build email parameters
  const formattedDateString = formatScanRequestDateString(firstRequest)
  const calendarLinks = getCalendar(firstRequest)

  const klavyioTemplate =
    existingScanRequests?.length > 0
      ? KLAVIYO_EVENTS.SCAN_REQUESTS_ADDED
      : KLAVIYO_EVENTS.USER_SCHEDULED_SCANS_CONFIRMATION

  // Send emails to users
  await Promise.all(
    users.map(async (user: User) => {
      let params = buildBaseEmailParams(
        firstRequest,
        scansList,
        updatedList,
        user.name,
        calendarLinks,
        formattedDateString
      )

      // Adjust parameters for SCAN_REQUESTS_ADDED template
      if (klavyioTemplate === KLAVIYO_EVENTS.SCAN_REQUESTS_ADDED) {
        params = {
          ...params,
          scansList: updatedList,
          scansListLength: updatedList.length,
          updatedList: scansList,
          updatedListLength: scansList.length
        }
      }

      // Send confirmation email if conditions are met
      if (
        existingScanRequests?.length ||
        (!existingScanRequests.length && rescheduledScanRequests?.length === 0)
      ) {
        await sendUserConfirmationEmail(user, klavyioTemplate, params)
      }
    })
  )

  // Handle rescheduled requests
  if (rescheduledScanRequests.length > 0) {
    const baseParams = buildBaseEmailParams(
      firstRequest,
      scansList,
      updatedList,
      '', // userName will be set per user
      calendarLinks,
      formattedDateString
    )
    await handleRescheduledRequests(
      rescheduledScanRequests,
      ids,
      users,
      baseParams
    )
  }

  logInfo(location, 'Sent scheduled confirmation emails to users.')

  // Send emails to scanners
  await sendScannerEmails(scheduledRequests)

  return scheduledRequests
}

export const markScanRequestAsUploaded = async (
  spaceId: string,
  scheduledFor: Date,
  user?: User
) => {
  logInfo(location, 'Marking scan request as uploaded.', {
    spaceId,
    scheduledFor
  })

  const sodScheduledFor = new Date(scheduledFor)
  sodScheduledFor.setHours(0, 0, 0, 0)

  const scanRequests = await scanRequestRepo.findScanRequestsByQuery({
    'space._id': spaceId
  })

  const authUser = (getAuthUser() as User) || user
  const uploadedBy = {
    _id: authUser._id,
    name: authUser.name,
    email: authUser.email
  }
  const space = await findSpaceById(spaceId)

  if (scanRequests) {
    logInfo(location, 'Scan requests found: ', JSON.stringify(scanRequests))

    const onlySkippedOrCanceled = scanRequests.every(
      (scanRequest) =>
        scanRequest.status === ScanRequestStatus.SKIPPED ||
        scanRequest.status === ScanRequestStatus.CANCELED
    )
    if (onlySkippedOrCanceled) {
      logInfo(
        location,
        'All scan requests are skipped or canceled. Creating new scan request.'
      )
      const newScanRequest = await scanRequestRepo.createScanRequests([
        {
          space,
          status: ScanRequestStatus.COMPLETED,
          uploadedAt: new Date(),
          uploadedBy,
          accessDetails: '',
          availableFrom: new Date(),
          community: {
            _id: space.community._id,
            name: space.community.name,
            organization: space.community.organization
          },
          isAutoScan: false,
          specialInstructions: '',
          requestedBy: {
            _id: authUser._id,
            name: authUser.name,
            email: authUser.email
          },
          scannedBy: {
            _id: authUser._id,
            name: authUser.name,
            email: authUser.email
          },
          scannedAt: new Date(),
          scheduledFor: sodScheduledFor,
          scheduledBy: {
            _id: authUser._id,
            name: authUser.name,
            email: authUser.email
          },
          spaceAvailabilityWhenCompleted: space.availabilityStatus,
          comment: 'Scan request created automatically for uploaded space.'
        }
      ])
      logInfo(
        location,
        'Created new scan request.',
        JSON.stringify(newScanRequest)
      )
    } else {
      logInfo(location, 'At least one scan request is not skipped or canceled.')

      let scanRequestToNotSkip: ScanRequest | undefined

      const completedWithSameDate = scanRequests.find(
        (scanRequest) =>
          scanRequest.status === ScanRequestStatus.COMPLETED &&
          isSameDay(new Date(scanRequest.scheduledFor), sodScheduledFor)
      )
      if (completedWithSameDate) {
        logInfo(
          location,
          'At least one scan request is completed for the same day.'
        )
        const updatedScanRequest = await completeScanRequest(
          completedWithSameDate._id.toString(),
          true
        )
        scanRequestToNotSkip = updatedScanRequest
        logInfo(
          location,
          'Marked scan request as uploaded.',
          JSON.stringify(updatedScanRequest)
        )
      }
      const scheduledWithSameDate = scanRequests.find(
        (scanRequest) =>
          scanRequest.status === ScanRequestStatus.SCHEDULED &&
          isSameDay(new Date(scanRequest.scheduledFor), sodScheduledFor)
      )

      if (!completedWithSameDate && scheduledWithSameDate) {
        logInfo(
          location,
          'At least one scan request is scheduled for the same day.'
        )
        const updatedScanRequest = await completeScanRequest(
          scheduledWithSameDate._id.toString(),
          true
        )
        scanRequestToNotSkip = updatedScanRequest
        logInfo(
          location,
          'Marked scan request as uploaded.',
          JSON.stringify(updatedScanRequest)
        )
      }

      if (!completedWithSameDate && !scheduledWithSameDate) {
        logInfo(
          location,
          'No scan request is completed or scheduled for the same day. Finding the most recent scheduled'
        )
        const lastScheduledScanRequest = scanRequests
          .filter(
            (scanRequest) => scanRequest.status === ScanRequestStatus.SCHEDULED
          )
          .sort(
            (a, b) => b.scheduledFor.getTime() - a.scheduledFor.getTime()
          )[0]
        if (lastScheduledScanRequest) {
          logInfo(
            location,
            'Found the most recent scheduled scan request.',
            JSON.stringify(lastScheduledScanRequest)
          )
          const updatedScanRequest = await completeScanRequest(
            lastScheduledScanRequest._id.toString(),
            true
          )
          scanRequestToNotSkip = updatedScanRequest
          logInfo(
            location,
            'Marked scan request as uploaded.',
            JSON.stringify(updatedScanRequest)
          )
        } else {
          logInfo(
            location,
            'No scan request is scheduled. Finding the most recent requested.'
          )
          const lastRequestedScanRequest = scanRequests
            .filter(
              (scanRequest) =>
                scanRequest.status === ScanRequestStatus.REQUESTED
            )
            .sort(
              (a, b) => b.requestedAt.getTime() - a.requestedAt.getTime()
            )[0]
          if (lastRequestedScanRequest) {
            logInfo(
              location,
              'Found the most recent requested scan request.',
              JSON.stringify(lastRequestedScanRequest)
            )
            const updatedScanRequest = await completeScanRequest(
              lastRequestedScanRequest._id.toString(),
              true
            )
            scanRequestToNotSkip = updatedScanRequest
            logInfo(
              location,
              'Marked scan request as uploaded.',
              JSON.stringify(updatedScanRequest)
            )
          } else {
            logInfo(
              location,
              'No scan request is requested. Creating new scan request.'
            )
            const newScanRequest = await scanRequestRepo.createScanRequests([
              {
                space,
                status: ScanRequestStatus.COMPLETED,
                uploadedAt: new Date(),
                uploadedBy,
                accessDetails: '',
                availableFrom: new Date(),
                community: {
                  _id: space.community._id,
                  name: space.community.name,
                  organization: space.community.organization
                },
                isAutoScan: false,
                specialInstructions: '',
                requestedBy: {
                  _id: authUser._id,
                  name: authUser.name,
                  email: authUser.email
                },
                scannedBy: {
                  _id: authUser._id,
                  name: authUser.name,
                  email: authUser.email
                },
                scannedAt: new Date(),
                scheduledFor: sodScheduledFor,
                scheduledBy: {
                  _id: authUser._id,
                  name: authUser.name,
                  email: authUser.email
                },
                comment:
                  'Scan request created automatically for uploaded space.',
                spaceAvailabilityWhenCompleted: space.availabilityStatus
              }
            ])
            logInfo(
              location,
              'Created new scan request.',
              JSON.stringify(newScanRequest)
            )
          }
        }
      }

      if (scanRequestToNotSkip) {
        logInfo(location, 'Skipping all other scan requests.')
        const resp = await Promise.all(
          scanRequests
            .filter(
              (scanRequest) =>
                scanRequest._id.toString() !==
                  scanRequestToNotSkip._id.toString() &&
                scanRequest.status !== ScanRequestStatus.COMPLETED &&
                scanRequest.status !== ScanRequestStatus.CANCELED &&
                scanRequest.status !== ScanRequestStatus.SKIPPED
            )
            .map((scanRequest) =>
              skipScanRequest(scanRequest._id.toString(), 'Unknown', authUser)
            )
        )
        logInfo(
          location,
          'Skipped all other scan requests.',
          JSON.stringify(resp)
        )
      }
    }
  } else {
    logInfo(location, 'Scan request not found. Creating new scan request.')
    const newScanRequest = await scanRequestRepo.createScanRequests([
      {
        space,
        status: ScanRequestStatus.COMPLETED,
        uploadedAt: new Date(),
        uploadedBy,
        accessDetails: '',
        availableFrom: new Date(),
        community: {
          _id: space.community._id,
          name: space.community.name,
          organization: space.community.organization
        },
        isAutoScan: false,
        specialInstructions: '',
        requestedBy: {
          _id: authUser._id,
          name: authUser.name,
          email: authUser.email
        },
        scannedBy: {
          _id: authUser._id,
          name: authUser.name,
          email: authUser.email
        },
        scannedAt: new Date(),
        scheduledFor: sodScheduledFor,
        scheduledBy: {
          _id: authUser._id,
          name: authUser.name,
          email: authUser.email
        },
        comment: 'Scan request created automatically for uploaded space.',
        spaceAvailabilityWhenCompleted: space.availabilityStatus
      }
    ])
    logInfo(
      location,
      'Created new scan request.',
      JSON.stringify(newScanRequest)
    )
    return
  }
}

export const cancelScanRequest = async (id: string, reason?: string) => {
  logInfo(location, 'Canceling scan request.', { scanRequestId: id, reason })
  if (!reason) throw new BadRequestError('Reason is required.')
  const scanRequest = await scanRequestRepo.findScanRequestById(id)
  if (!scanRequest) throw new NotFoundError('Scan request not found.')
  const authUser = getAuthUser() as User
  const canceledBy = {
    _id: authUser._id,
    name: authUser.name,
    email: authUser.email
  }

  const spaceAvailabilityStatus = (
    await findSpaceById(scanRequest.space._id.toString(), 'availabilityStatus')
  )?.availabilityStatus

  const result = await scanRequestRepo.updateScanRequest(
    { _id: id },
    {
      status: ScanRequestStatus.CANCELED,
      reason,
      canceledBy,
      canceledAt: new Date(),
      ...(spaceAvailabilityStatus && {
        spaceAvailabilityWhenCanceled: spaceAvailabilityStatus
      })
    }
  )

  logInfo(location, 'Scan request canceled.', result)
  return result
}

const getScanAndUpdateList = async (
  jobId: string,
  statuses: ScanRequestStatusList
): Promise<GetListResponse> => {
  const scanRequests = await findScanRequestsByQuery({
    deletedAt: null,
    status: { $in: Object.values(statuses) },
    jobId
  })

  const scansList = formatScanRequests(
    scanRequests.filter(
      (scanRequest) => scanRequest.status === statuses.forList
    )
  )
  const updatedList = formatScanRequests(
    scanRequests.filter(
      (scanRequest) => scanRequest.status === statuses.forUpdate
    )
  )

  return { scansList, updatedList }
}

export const skipScanRequest = async (
  id: string,
  reason?: string,
  adminUser?: User,
  comment?: string
) => {
  logInfo(location, 'Skipping scan request.', { scanRequestId: id, reason })
  if (!reason) throw new BadRequestError('Reason is required.')
  const scanRequest = await scanRequestRepo.findScanRequestById(id)
  if (!scanRequest) throw new NotFoundError('Scan request not found.')

  const spaceAvailabilityStatus = (
    await findSpaceById(scanRequest.space._id.toString(), 'availabilityStatus')
  )?.availabilityStatus

  switch (scanRequest.status) {
    case ScanRequestStatus.CANCELED: {
      logWarn(
        location,
        'Tried to SKIP a CANCELED scan request.',
        JSON.stringify(scanRequest)
      )
      return scanRequest
    }
    case ScanRequestStatus.COMPLETED: {
      logWarn(
        location,
        'Tried to SKIP a COMPLETED scan request.',
        JSON.stringify(scanRequest)
      )
      return scanRequest
    }
    case ScanRequestStatus.SKIPPED: {
      logInfo(location, 'Skipping a SKIPPED scan request.', {
        scanRequestId: id,
        reason
      })
      if (reason === scanRequest.reason) {
        logInfo(location, 'Reason is same as previous reason')
        return scanRequest
      }
      return await scanRequestRepo.updateScanRequest(
        { _id: id },
        {
          reason,
          comment,
          ...(spaceAvailabilityStatus && {
            spaceAvailabilityWhenSkipped: spaceAvailabilityStatus
          })
        }
      )
    }

    default: {
      let skippedBy: Pick<User, '_id' | 'name' | 'email'>
      if (adminUser) {
        logDebug(location, 'Skipped by admin user.', adminUser)
        skippedBy = {
          _id: adminUser._id.toString(),
          name: adminUser.name,
          email: adminUser.email
        }
      } else {
        const authUser = getAuthUser() as User
        skippedBy = {
          _id: authUser._id.toString(),
          name: authUser.name,
          email: authUser.email
        }
      }

      const result = await scanRequestRepo.updateScanRequest(
        { _id: id },
        {
          status: ScanRequestStatus.SKIPPED,
          skippedAt: new Date(),
          skippedBy,
          reason,
          comment,
          ...(spaceAvailabilityStatus && {
            spaceAvailabilityWhenSkipped: spaceAvailabilityStatus
          })
        }
      )
      if (!result) {
        throw new BadRequestError('Error in skipping scan request.')
      }
      if (reason === 'Ran out of time') {
        await scanRequestRepo.createScanRequests([
          {
            ...result,
            status: ScanRequestStatus.REQUESTED,
            scheduledFor: undefined,
            requestedAt: new Date(),
            skippedAt: undefined,
            skippedBy: undefined,
            reason: undefined,
            createdAt: undefined,
            updatedAt: undefined,
            _id: undefined,
            comment: undefined,
            spaceAvailabilityWhenSkipped: undefined
          }
        ])
      }
      return result
    }
  }
}

export const scheduleScanRequest = async (
  id: string,
  scheduledFor?: Date,
  scannerArrivalWindow?: { from: string; to: string },
  scannerEmail?: string
): Promise<ScanRequest> => {
  logInfo(location, 'Scheduling scan request.', id)
  if (!scheduledFor) throw new BadRequestError('Scheduled for is required.')
  if (!isValid(new Date(scheduledFor)))
    throw new BadRequestError('Scheduled for is not valid.')

  const currentScheduledFor = new Date(scheduledFor)
  currentScheduledFor.setHours(0, 0, 0, 0)
  const now = new Date()
  now.setHours(0, 0, 0, 0)
  if (currentScheduledFor < now) {
    throw new BadRequestError('Scheduled for date cannot be in the past.')
  }

  const scanRequest = await scanRequestRepo.findScanRequestById(id)
  const previousScheduledForDate = scanRequest.scheduledFor
  const previousScannerArrivalWindow = scanRequest.scannerArrivalWindow

  if (!scanRequest) throw new NotFoundError('Scan request not found.')

  if (
    scanRequest.status !== ScanRequestStatus.REQUESTED &&
    scanRequest.status !== ScanRequestStatus.SCHEDULED
  ) {
    throw new BadRequestError('Scan request cannot be scheduled.')
  }

  const authUser = getAuthUser()

  const scheduledBy = {
    _id: authUser._id,
    name: authUser.name,
    email: authUser.email
  }

  const spaceAvailabilityStatus = (
    await findSpaceById(scanRequest.space._id.toString(), 'availabilityStatus')
  )?.availabilityStatus

  try {
    const updateResult = await scanRequestRepo.updateScanRequest(
      { _id: id },
      {
        status: ScanRequestStatus.SCHEDULED,
        scheduledFor: currentScheduledFor,
        scheduledBy,
        scheduledAt: new Date(),
        scannerArrivalWindow,
        ...(spaceAvailabilityStatus && {
          spaceAvailabilityWhenScheduled: spaceAvailabilityStatus
        }),
        scannerEmail,
        isRescheduled: !!scanRequest.scheduledFor,
        previousScheduledForDate,
        previousScannerArrivalWindow
      }
    )
    logInfo(location, 'Scan request scheduled.', updateResult)

    return updateResult
  } catch (error) {
    throw new BadRequestError(
      `Error updating scan request with id: ${id}: ${error.message}`
    )
  }
}

export const deleteScanRequestById = async (
  _id: string
): Promise<ScanRequest> => {
  const authUser = getAuthUser() as User
  const deletedBy = {
    _id: authUser._id,
    name: authUser.name,
    email: authUser.email
  }

  return await scanRequestRepo.deleteScanRequest({ _id, deletedBy })
}

export const findScanRequestById = async (
  _id: string
): Promise<ScanRequest> => {
  return await scanRequestRepo.findScanRequestById(_id)
}

export const findScanRequestsCountByCommunities = async (
  query: FindScanRequestsCountByCommunitiesDTO
): Promise<ScanRequest[]> => {
  return await scanRequestRepo.findScanRequestsCountByCommunities(query)
}

const findSpace = async (spaceId: string, fields: string) => {
  const result = (
    await findSpacesByQuery({ _id: spaceId }, fields, {
      skipAclScope: true
    })
  )[0]
  if (!result) throw new NotFoundError('Space not found')
  return result
}

const isRequestedByAdmin = (scanRequest: ScanRequest) =>
  scanRequest?.requestedBy?.email?.includes('@peek.us')

const maskRequestedBy = (scanRequest: ScanRequest) => {
  const isAdmin = isRequestedByAdmin(scanRequest)
  return {
    _id: scanRequest.requestedBy._id,
    name: isAdmin ? 'Peek Team' : scanRequest.requestedBy.name,
    email: isAdmin ? '<EMAIL>' : scanRequest.requestedBy.email
  }
}

export const generateJobId = (
  communityId: string,
  scheduledFor: Date
): string => {
  return `${communityId}-${scheduledFor
    .toISOString()
    .split('T')[0]
    .replace(/-/g, '')}`
}

export const groupScanRequests = ({
  groupBy,
  groupByName,
  scanRequests
}: GroupScanRequestParams): GroupScanRequest[] => {
  const valueName = groupByName ? groupByName : 'data'

  const resultGroupByDateMap = scanRequests.reduce<
    Record<string, ScanRequest[]>
  >((acc, scanRequest) => {
    let value = scanRequest[groupBy]

    if (
      !value &&
      scanRequest.scheduledFor &&
      scanRequest.community?._id &&
      groupBy === 'jobId'
    ) {
      const date = format(scanRequest.scheduledFor, 'yyyyMMdd')
      value = `${scanRequest.community._id}-${date}`
    }

    if (value) {
      if (isDate(value)) value = value.toISOString().split('T')[0]
      if (!acc[value]) acc[value] = []
      acc[value].push(scanRequest)
    }
    return acc
  }, {})

  return Object.keys(resultGroupByDateMap).reduce((acc, key) => {
    acc.push({ date: key, [valueName]: resultGroupByDateMap[key] })
    return acc
  }, [])
}

const formatScanRequest = (
  scanRequest: ScanRequest,
  index: number
): FormattedScanRequest => ({
  order: index + 1,
  unit: scanRequest.space?.unit,
  floorPlan: scanRequest.space?.floorPlan?.name,
  reason: scanRequest.reason
})

const formatScanRequests = (
  scanRequests: ScanRequest[]
): FormattedScanRequest[] => scanRequests.map(formatScanRequest)

export const getRecipients = async (
  communityId: string,
  aliases: RoleAliases[],
  userTag: UserTag
) => {
  const rolesQuery = { alias: { $in: aliases } }
  logDebug(location, 'Roles query:', rolesQuery)
  const roles = await findRolesByQuery(rolesQuery, '_id', {
    skipAclScope: true
  })
  logDebug(location, 'Roles found:', roles)

  const userQuery = {
    'communities._id': { $in: [communityId] },
    status: 'active',
    roleId: {
      $in: [...roles.map((role) => role._id.toString())]
    }
  }
  logDebug(location, 'User query:', userQuery)

  if (userTag) {
    Object.entries(userTag).map(([key, value]) => {
      userQuery[key] = value
    })
    logDebug(location, 'User query with tags:', userQuery)
  }

  return await findUsers(userQuery, { skipAclScope: true })
}

const getJobId = (scanRequest: ScanRequest): string => {
  let jobId = scanRequest?.jobId
  if (!jobId && scanRequest?.community?._id && scanRequest.scheduledFor) {
    jobId = `${scanRequest.community._id.toString()}-${format(
      scanRequest.scheduledFor,
      'yyyy-MM-dd'
    )}`
  }
  return jobId
}

const formatScanRequestDateString = (scanRequest: ScanRequest) => {
  if (
    !scanRequest.scheduledFor ||
    !scanRequest.scannerArrivalWindow.from ||
    !scanRequest.scannerArrivalWindow.to
  ) {
    return undefined
  }

  return `${new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric'
  }).format(scanRequest.scheduledFor)} at ${
    scanRequest.community.name
  }. The scanner will arrive between ${
    scanRequest.scannerArrivalWindow.from
  } and ${scanRequest.scannerArrivalWindow.to}`
}

const formatCanceledScanRequestDateString = (scanRequest: ScanRequest) => {
  if (
    !scanRequest.scheduledFor ||
    !scanRequest.scannerArrivalWindow.from ||
    !scanRequest.scannerArrivalWindow.to
  ) {
    return undefined
  }

  const textTemplate = 'The scanner will arrive between {from} and {to}...'
  return textTemplate
    .replace('{from}', scanRequest.scannerArrivalWindow.from)
    .replace('{to}', scanRequest.scannerArrivalWindow.to)
}

export const getCalendar = (scanRequest: ScanRequest) => {
  if (!scanRequest.scannerArrivalWindow) return undefined

  const year = scanRequest.scheduledFor.getFullYear()
  const month = scanRequest.scheduledFor.getMonth()
  const day = scanRequest.scheduledFor.getDate()

  const extractTimeParts = (timeStr: string) => {
    const [hour, minuteWithMeridiem] = timeStr.split(':') // Ex: ['04', '00 p.m.']
    const minute = minuteWithMeridiem.substring(0, 2) // '00'
    const meridiem = minuteWithMeridiem.toLowerCase().includes('p.m.')
      ? 'PM'
      : 'AM'

    let hour24 = Number.parseInt(hour, 10)

    if (meridiem === 'PM' && hour24 !== 12) {
      hour24 += 12
    } else if (meridiem === 'AM' && hour24 === 12) {
      hour24 = 0
    }

    return { hour24, minute: Number.parseInt(minute, 10) }
  }

  const { hour24: fromHour, minute: fromMinute } = extractTimeParts(
    scanRequest.scannerArrivalWindow.from
  )
  const { hour24: toHour, minute: toMinute } = extractTimeParts(
    scanRequest.scannerArrivalWindow.to
  )

  const startDate = new Date(year, month, day, fromHour, fromMinute)
  const endDate = new Date(year, month, day, toHour, toMinute)

  const description = `This is the scanner's arrival window - the actual photoshoot might go longer depending on the number of spaces being captured.<br>
 Visit Peek's dashboard to a more complete view on the photoshoots scheduled for the day: ${process.env.AGENT_DASHBOARD_WEB_URL}/scan-requests/upcoming-shoots.<br>Please take a look at the Photoshoot Prep Guide if you haven't done so yet: ${process.env.PHOTOSHOOT_PREP_GUIDE_URL}.`

  return buildCalendarLinks({
    title: `Peek Photoshoot at ${scanRequest.community.name}`,
    description: description,
    location: scanRequest.community.name,
    start: startDate,
    end: endDate
  })
}
