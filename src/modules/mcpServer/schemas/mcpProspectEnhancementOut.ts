import { z } from 'zod'
import { ObjectIdSchema } from './common'

export const IdealBedroomSchema = z.object({
  duration: z.number(),
  sessions: z.number(),
  bucket: z.string()
})

export const IdealFloorPlanSchema = z.object({
  spaceFloorPlan: z.string(),
  duration: z.number()
})

export const IdealRentalPriceSchema = z.object({
  sessions: z.number(),
  duration: z.number(),
  price: z.number(),
  bucket: z.string()
})

export const ProspectSchema = z.object({
  _id: z.any().optional(),
  email: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional()
})

export const ProspectEventSourceSchema = z.object({
  system: z.string(),
  endpoint: z.string(),
  propertyId: z.string().optional(),
  agentName: z.string().optional(),
  rawRef: z.string().optional()
})

export const ProspectEventPayloadSchema = z.object({
  patches: z.array(z.any())
})

export const ProspectEventSchema = z.object({
  _id: ObjectIdSchema,
  prospectId: ObjectIdSchema,
  externalId: z.string(),
  extractionId: z.string(),
  type: z.string(),
  occurredAt: z.date(),
  receivedAt: z.date(),
  source: ProspectEventSourceSchema,
  eventReasons: z.array(z.string()),
  payload: ProspectEventPayloadSchema,
  createdAt: z.date().optional(),
  updatedAt: z.date().optional()
})

export const ProspectEnhancementOutSchema = z.object({
  _id: ObjectIdSchema,
  prospectId: ObjectIdSchema,

  prospect: ProspectSchema.optional(),

  prospectIntelligence: z.any().optional(),

  activities: z.array(z.any()).optional(),

  roomsViewed: z
    .object({
      total: z.number(),
      rooms: z.array(z.any())
    })
    .optional(),

  totalSessions: z.number().optional(),
  totalEngagement: z.number().optional(),
  totalSpacesViews: z.number().optional(),

  idealBedrooms: z
    .object({
      total: z.number(),
      idealBedrooms: z.array(IdealBedroomSchema)
    })
    .optional(),

  idealPrices: z
    .object({
      total: z.number(),
      idealPrices: z.array(IdealRentalPriceSchema)
    })
    .optional(),

  idealFloorPlans: z
    .object({
      total: z.number(),
      floorPlans: z.array(IdealFloorPlanSchema)
    })
    .optional(),

  score: z
    .object({
      piScore: z.number(),
      piStatus: z.string()
    })
    .optional(),

  prospectEvents: z
    .object({
      total: z.number(),
      events: z.array(ProspectEventSchema)
    })
    .optional(),

  errors: z
    .array(
      z.object({
        endpoint: z.string(),
        error: z.string()
      })
    )
    .optional()
})

export type ProspectEnhancementOut = z.infer<
  typeof ProspectEnhancementOutSchema
>
