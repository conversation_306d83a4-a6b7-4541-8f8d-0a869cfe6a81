import { Router, Request, Response } from 'express'
import { OAuthProtectedResourceMetadata } from '@modelcontextprotocol/sdk/shared/auth.js'

export const mcpMetaDataRoutes = Router()

const ISSUER = `https://${process.env.AUTH0_DOMAIN}`
const mcpServerUrl = process.env.MCP_SERVER_URL || 'http://localhost:4000/mcp'

mcpMetaDataRoutes.get(
  '/.well-known/oauth-protected-resource',
  (req: Request, res: Response) => {
    const resourceUrl = new URL(mcpServerUrl).href.replace(/\/$/, '')
    const authServerUrl = new URL(ISSUER).href.replace(/\/$/, '')

    const protectedResourceMetadata: OAuthProtectedResourceMetadata = {
      resource: resourceUrl,
      authorization_servers: [authServerUrl],
      scopes_supported: ['mcp:tools', 'read', 'write'],
      resource_name: 'Peek MCP Server',
      bearer_methods_supported: ['header'],
      resource_signing_alg_values_supported: ['RS256'],
      introspection_endpoint_auth_methods_supported: [
        'client_secret_basic',
        'client_secret_post',
        'bearer'
      ],
      token_types_supported: ['Bearer', 'DPoP'],
      response_types_supported: ['code'],
      grant_types_supported: ['authorization_code', 'refresh_token'],
      code_challenge_methods_supported: ['S256'],
      ui_locales_supported: ['en-US']
    }

    res.setHeader('Content-Type', 'application/json')
    res.setHeader('Cache-Control', 'max-age=3600')

    res.json(protectedResourceMetadata)
  }
)

mcpMetaDataRoutes.get(
  '/.well-known/oauth-authorization-server',
  async (req: Request, res: Response) => {
    try {
      const authServerMetadataUrl = `${ISSUER}/.well-known/openid-configuration`

      const response = await fetch(authServerMetadataUrl)
      if (!response.ok) {
        throw new Error(
          `Failed to fetch authorization server metadata: ${response.statusText}`
        )
      }

      const metadata = await response.json()

      // @ts-expect-error: metadata doesn't guarantee those values.
      metadata.issuer = ISSUER
      // @ts-expect-error: metadata doesn't guarantee those values.
      metadata.resource_indicators_supported = true
      // @ts-expect-error: metadata doesn't guarantee those values.
      metadata.authorization_response_iss_parameter_supported = true

      // @ts-expect-error: metadata doesn't guarantee those values.
      metadata.code_challenge_methods_supported =
        // @ts-expect-error: metadata doesn't guarantee those values.
        metadata.code_challenge_methods_supported || ['S256']

      res.setHeader('Content-Type', 'application/json')
      res.setHeader('Cache-Control', 'max-age=3600')

      res.json(metadata)
    } catch (error) {
      console.error('Error fetching authorization server metadata:', error)
      res.status(500).json({
        error: 'server_error',
        error_description: 'Failed to fetch authorization server metadata'
      })
    }
  }
)

export default mcpMetaDataRoutes
