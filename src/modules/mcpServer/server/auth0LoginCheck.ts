import {
  extractPermissionsAndRestrictions,
  findPoliciesByRoleAndUserId
} from '@modules/users/services/policy'
import { findUserByEmail } from '@modules/users/services/user'
import { Router, Request, Response } from 'express'
import jwt from 'jsonwebtoken'
import { logError } from '@core/log'

const PEEK_API_JWT_SECRET = process.env.PEEK_API_JWT_SECRET!
const AUTH0_DOMAIN = process.env.AUTH0_DOMAIN!
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:4000/mcp'

export const auth0Routes = Router()

auth0Routes.post('/oauth/introspect', async (req: Request, res: Response) => {
  const { token } = req.body

  if (!token) {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'Token parameter is required'
    })
  }

  try {
    const payload = jwt.verify(token, PEEK_API_JWT_SECRET, {
      algorithms: ['HS256']
    }) as any

    const now = Math.floor(Date.now() / 1000)
    const isActive = payload.exp > now

    if (!isActive) {
      return res.json({ active: false })
    }

    res.json({
      active: true,
      scope: payload.scope || 'mcp:tools read write',
      client_id: payload.client_id || 'peek-mcp-client',
      username: payload.email,
      token_type: 'Bearer',
      exp: payload.exp,
      iat: payload.iat,
      nbf: payload.nbf || payload.iat,
      sub: payload.sub || payload.userId,
      aud: payload.aud || MCP_SERVER_URL,
      iss: payload.iss || `https://${AUTH0_DOMAIN}/`,
      jti: payload.jti
    })
  } catch (err) {
    logError('auth0-introspect', 'Token introspection failed', { error: err })
    return res.json({ active: false })
  }
})

auth0Routes.post('/auth0-login-check', async (req: Request, res: Response) => {
  const authHeader = req.headers.authorization

  if (!authHeader?.startsWith('Bearer ')) {
    return res.status(401).json({
      error: 'invalid_request',
      error_description: 'Missing or invalid Authorization header'
    })
  }

  const token = authHeader.split(' ')[1]

  try {
    const payload = jwt.verify(token, PEEK_API_JWT_SECRET, {
      algorithms: ['HS256']
    }) as { email: string; purpose: string }

    if (payload.purpose !== 'peek-api-auth-check') {
      return res.status(403).json({
        error: 'invalid_token',
        error_description: 'Invalid token purpose'
      })
    }

    const user = await findUserByEmail(payload.email)

    if (!user || !user.status || user.status !== 'active') {
      return res.status(401).json({
        error: 'invalid_token',
        error_description: 'User not found or inactive'
      })
    }

    const policies = await findPoliciesByRoleAndUserId(
      user.roleId.toString(),
      user._id.toString()
    )

    const { allow, deny } = extractPermissionsAndRestrictions(policies)

    const communities: string[] = []
    const organizations: string[] = []

    policies.forEach((policy) => {
      if (policy.resource === 'communities' && policy.communityId) {
        communities.push(policy.communityId.toString())
      }

      if (policy.resource === 'organizations' && policy.organizationId) {
        organizations.push(policy.organizationId.toString())
      }
    })

    const uniqueCommunities = Array.from(new Set(communities))
    const uniqueOrganizations = Array.from(new Set(organizations))

    const enhancedToken = jwt.sign(
      {
        sub: user._id.toString(),
        email: user.email,
        iss: `https://${AUTH0_DOMAIN}/`,
        aud: MCP_SERVER_URL,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60,
        scope: 'mcp:tools read write',
        userId: user._id.toString(),
        communities: uniqueCommunities,
        organizations: uniqueOrganizations,
        permissions: { allow, deny },
        role: user.role._id
      },
      PEEK_API_JWT_SECRET,
      { algorithm: 'HS256' }
    )

    res.json({
      access_token: enhancedToken,
      token_type: 'Bearer',
      expires_in: 86400,
      scope: 'mcp:tools read write',

      email: user.email,
      userId: user._id.toString(),
      communities: uniqueCommunities,
      organizations: uniqueOrganizations,
      permissions: { allow, deny },
      role: user.role._id
    })
  } catch (err) {
    logError('auth0-login', 'Auth0 verification failed', { error: err })

    if (err.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'invalid_token',
        error_description: 'Token has expired'
      })
    }

    return res.status(401).json({
      error: 'invalid_token',
      error_description: 'Invalid or expired token'
    })
  }
})
