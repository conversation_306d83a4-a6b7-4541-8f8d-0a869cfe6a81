import { Schema, model } from 'mongoose'
import { ProspectEvent } from '../types/prospectEvent'

const prospectEventSourceSchema = new Schema(
  {
    system: { type: String, required: true },
    endpoint: { type: String, required: true },
    propertyId: { type: String },
    agentName: { type: String },
    rawRef: { type: String }
  },
  { _id: false }
)

const prospectEventPayloadSchema = new Schema(
  {
    patches: [Schema.Types.Mixed]
  },
  { _id: false }
)

const prospectEventSchema = new Schema<ProspectEvent>(
  {
    prospectId: {
      type: Schema.Types.ObjectId,
      required: true,
      index: true
    },
    externalId: {
      type: String,
      required: true
    },
    extractionId: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true,
      index: true
    },
    occurredAt: {
      type: Date,
      required: true,
      index: true
    },
    receivedAt: {
      type: Date,
      required: true
    },
    prospect: {
      _id: {
        type: String,
        required: true,
        index: true
      },
      externalIds: {
        type: Object,
        default: {}
      }
    },
    source: {
      type: prospectEventSourceSchema,
      required: true
    },
    eventReasons: {
      type: [String],
      default: []
    },
    payload: {
      type: prospectEventPayloadSchema,
      required: true
    }
  },
  {
    timestamps: true,
    collection: 'prospectevents'
  }
)

prospectEventSchema.index({ prospectId: 1, occurredAt: -1 })
prospectEventSchema.index({ prospectId: 1, type: 1 })
prospectEventSchema.index({ externalId: 1 })
prospectEventSchema.index({ extractionId: 1 })

export const ProspectEventsModel = model<ProspectEvent>(
  'prospectevents',
  prospectEventSchema
)
