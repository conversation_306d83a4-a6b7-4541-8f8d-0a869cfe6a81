service: peek-backend-dx-serverless

custom:
  enabled:
    dev: false
    prod: true
  sentry:
    enabled:
      prod: true
      dev: false
  esbuild:
    bundle: true
    packager: yarn
    platform: node
    target: node18
    concurrency: 4
    format: cjs
    banner:
      js: |
        const Sentry = require("@sentry/aws-serverless");

        const consoleLevels = [];
        if (process.env.ENABLE_SENTRY_CONSOLE_ERROR === "true") {
          consoleLevels.push("error");
        }
        if (process.env.ENABLE_SENTRY_CONSOLE_WARN === "true") {
          consoleLevels.push("warn");
        }

        Sentry.init({
          dsn: process.env.SENTRY_DSN,
          enabled: process.env.SENTRY_ENABLED?.toString() === 'true',
          integrations: [Sentry.captureConsoleIntegration({ levels: consoleLevels })],
        });
        Sentry.setTag("stack_name", "${self:service}-${self:provider.stage}");
    footer:
      js: |
        module.exports = { 
          handler: Sentry.wrapHandler(handler)
        };
    exclude:
      - '*.node'

provider:
  name: aws
  runtime: nodejs18.x
  stage: ${opt:stage, "dev"}
  region: 'us-east-1'
  memorySize: 512
  timeout: 10
  logRetentionInDays: 90
  versionFunctions: false
  layers:
    - arn:aws:lambda:us-east-1:943013980633:layer:SentryNodeServerlessSDKv9:11
  environment:
    SENTRY_DSN: ${ssm:/${self:provider.stage}/SENTRY_SERVERLESS_DSN}
    SENTRY_ENABLED: ${self:custom.sentry.enabled.${opt:stage, 'dev'}}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - SQS:SendMessage
        - SQS:DeleteMessage
        - SQS:ReceiveMessage
        - SQS:GetQueueUrl
        - SQS:ListQueues
      Resource: '*'
    - Effect: 'Allow'
      Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:Receive
      Resource: '*'
    - Effect: 'Allow'
      Action:
        - secretsmanager:ListSecrets
        - secretsmanager:GetSecretValue
        - secretsmanager:PutSecretValue
        - secretsmanager:UpdateSecret
      Resource: '*'

functions:
  syncBozzutoStartFunction:
    handler: src/modules/bozzuto/serverless/syncBozzutoMits.handler
    name: ${self:provider.stage}-sync-bozzuto-mits-start-function
    reservedConcurrency: 1
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      BOZZUTO_FTP_URL: ${ssm:/${self:provider.stage}/BOZZUTO_FTP_URL}
      BOZZUTO_FTP_PORT: ${ssm:/${self:provider.stage}/BOZZUTO_FTP_PORT}
      BOZZUTO_FTP_USER: ${ssm:/${self:provider.stage}/BOZZUTO_FTP_USER}
      BOZZUTO_FTP_PASS: ${ssm:/${self:provider.stage}/BOZZUTO_FTP_PASS}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
    events:
      - schedule:
          rate: cron(30 3 * * ? *) # 3:30am UTC = 11:30am EST
  sendDynamicsProspect:
    handler: src/modules/gsDynamics/serverless/prospect/sendProspect.handler
    name: ${self:provider.stage}-send-dynamics-prospect
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}

      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    timeout: 900
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - SendDynamicsProspectQueue
              - Arn
  sendDynamicsAppointments:
    handler: src/modules/gsDynamics/serverless/appointment/sendAppointment.handler
    name: ${self:provider.stage}-send-dynamics-appointments
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}

      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    timeout: 900
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - SendDynamicsAppointmentQueue
              - Arn
  updateDynamicsAppointments:
    handler: src/modules/gsDynamics/serverless/appointment/updateAppointment.handler
    name: ${self:provider.stage}-update-dynamics-appointments
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - UpdateDynamicsAppointmentQueue
              - Arn
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - CompleteDynamicsAppointmentQueue
              - Arn
  cancelDynamicsAppointments:
    handler: src/modules/gsDynamics/serverless/appointment/cancelAppointment.handler
    name: ${self:provider.stage}-cancel-dynamics-appointments
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    timeout: 900
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - CancelDynamicsAppointmentQueue
              - Arn
  sendDynamicsVirtualTour:
    handler: src/modules/gsDynamics/serverless/virtualTour/sendVirtualTour.handler
    name: ${self:provider.stage}-send-dynamics-virtual-tour
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    timeout: 900
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - SendDynamicsVirtualTourQueue
              - Arn
  syncConsolidatedDynamicsVirtualTours:
    handler: src/modules/gsDynamics/serverless/virtualTour/syncConsolidatedVirtualTours.handler
    name: ${self:provider.stage}-sync-consolidated-dynamics-virtual-tours
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - schedule:
          rate: rate(4 hours)
  EntrataSendProspectFunction:
    handler: src/modules/entrata/serverless/sendEntrataProspect.handler
    name: ${self:provider.stage}-entrata-send-prospect-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      ENTRATA_API_KEY: ${ssm:/${self:provider.stage}/ENTRATA_API_KEY}
      ENTRATA_API_URL: ${ssm:/${self:provider.stage}/ENTRATA_API_URL}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - EntrataSendProspectQueue
              - Arn
  FunnelSendProspectFunction:
    handler: src/modules/funnel/serverless/sendFunnelProspect.handler
    name: ${self:provider.stage}-funnel-send-prospect-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - FunnelSendProspectQueue
              - Arn
  AnyoneHomeSendProspectFunction:
    handler: src/modules/anyoneHome/serverless/sendAnyoneHomeProspect.handler
    name: ${self:provider.stage}-anyonehome-send-prospect-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      ANYONE_HOME_PASSWORD: ${ssm:/peek-backend/dx/${self:provider.stage}/anyone_home}
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - AnyOneHomeProspectQueue
              - Arn
  YardiSendProspectFunction:
    handler: src/modules/yardi/serverless/sendYardiProspect.handler
    name: ${self:provider.stage}-yardi-send-prospect-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - YardiSendProspectQueue
              - Arn
  KnockSendProspectFunction:
    handler: src/modules/knock/serverless/sendKnockProspect.handler
    name: ${self:provider.stage}-knock-send-prospect-serverless
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      PEEK_ENVIRONMENT: ${self:provider.stage}
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - KnockSendProspectQueue
              - Arn
  KnockSendMailFunction:
    handler: src/modules/knock/serverless/sendKnockMail.handler
    name: ${self:provider.stage}-knock-send-mail-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - KnockSendMailQueue
              - Arn
  FunnelScheduleSgtTourFunction:
    handler: src/modules/funnel/serverless/sgt/scheduleSgtTour.handler
    name: ${self:provider.stage}-funnel-schedule-sgt-tour-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - FunnelScheduleSgtTourQueue
              - Arn
  FunnelRescheduleSgtTourFunction:
    handler: src/modules/funnel/serverless/sgt/rescheduleSgtTour.handler
    name: ${self:provider.stage}-funnel-reschedule-sgt-tour-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - FunnelRescheduleSgtTourQueue
              - Arn
  FunnelCancelSgtTourFunction:
    handler: src/modules/funnel/serverless/sgt/cancelSgtTour.handler
    name: ${self:provider.stage}-funnel-cancel-sgt-tour-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - FunnelCancelSgtTourQueue
              - Arn
  FunnelCompleteSgtTour:
    handler: src/modules/funnel/serverless/sgt/completeSgtTour.handler
    name: ${self:provider.stage}-funnel-complete-sgt-tour-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - FunnelCompleteSgtTourQueue
              - Arn
  EntrataScheduleSgtTourFunction:
    handler: src/modules/entrata/serverless/sgt/scheduleSgtTour.handler
    name: ${self:provider.stage}-entrata-schedule-sgt-tour-serverless
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      ENTRATA_API_KEY: ${ssm:/${self:provider.stage}/ENTRATA_API_KEY}
      ENTRATA_API_URL: ${ssm:/${self:provider.stage}/ENTRATA_API_URL}
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - EntrataScheduleSgtTourQueue
              - Arn
  EntrataRescheduleSgtTourFunction:
    handler: src/modules/entrata/serverless/sgt/rescheduleSgtTour.handler
    name: ${self:provider.stage}-entrata-reschedule-sgt-tour-serverless
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      ENTRATA_API_KEY: ${ssm:/${self:provider.stage}/ENTRATA_API_KEY}
      ENTRATA_API_URL: ${ssm:/${self:provider.stage}/ENTRATA_API_URL}
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - EntrataRescheduleSgtTourQueue
              - Arn
  EntrataCancelSgtTourFunction:
    handler: src/modules/entrata/serverless/sgt/cancelSgtTour.handler
    name: ${self:provider.stage}-entrata-cancel-sgt-tour-serverless
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      ENTRATA_API_KEY: ${ssm:/${self:provider.stage}/ENTRATA_API_KEY}
      ENTRATA_API_URL: ${ssm:/${self:provider.stage}/ENTRATA_API_URL}
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - EntrataCancelSgtTourQueue
              - Arn
  EntrataCompleteSgtTour:
    handler: src/modules/entrata/serverless/sgt/completeSgtTour.handler
    name: ${self:provider.stage}-entrata-complete-sgt-tour-serverless
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      ENTRATA_API_KEY: ${ssm:/${self:provider.stage}/ENTRATA_API_KEY}
      ENTRATA_API_URL: ${ssm:/${self:provider.stage}/ENTRATA_API_URL}
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - EntrataCompleteSgtTourQueue
              - Arn
          functionResponseType: ReportBatchItemFailures
  YardiScheduleSgtTourFunction:
    handler: src/modules/yardi/serverless/sgt/scheduleSgtTour.handler
    name: ${self:provider.stage}-yardi-schedule-sgt-tour-serverless
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - YardiScheduleSgtTourQueue
              - Arn
  YardiCreateSgtEventFunction:
    handler: src/modules/yardi/serverless/sgt/createSgtEvent.handler
    name: ${self:provider.stage}-yardi-create-sgt-event-serverless
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
    reservedConcurrency: 5
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - YardiCreateSgtEventQueue
              - Arn
  YardiRescheduleSgtTourFunction:
    handler: src/modules/yardi/serverless/sgt/rescheduleSgtTour.handler
    name: ${self:provider.stage}-yardi-reschedule-sgt-tour-serverless
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - YardiRescheduleSgtTourQueue
              - Arn
  YardiCancelSgtTourFunction:
    handler: src/modules/yardi/serverless/sgt/cancelSgtTour.handler
    name: ${self:provider.stage}-yardi-cancel-sgt-tour-serverless
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
    reservedConcurrency: 5
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - YardiCancelSgtTourQueue
              - Arn
  YardiCompleteSgtTour:
    handler: src/modules/yardi/serverless/sgt/completeSgtTour.handler
    name: ${self:provider.stage}-yardi-complete-sgt-tour-serverless
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - YardiCompleteSgtTourQueue
              - Arn
  CreateSpaceAsyncFunction:
    handler: src/modules/spacex/services/createSpace.handler
    name: ${self:provider.stage}-create-space-async-serverless
    reservedConcurrency: 18
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
      SPACE_CHANGE_TOPIC: ${ssm:/${self:provider.stage}/SPACE_CHANGE_TOPIC}
    timeout: 30
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - CreateSpaceAsyncQueueFifo
              - Arn
  UpdateRentCafeV2Token:
    handler: src/modules/rentCafeV2/serverless/updateRentCafeV2Token.handler
    name: ${self:provider.stage}-update-rent-cafe-v2-token
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - schedule:
          rate: rate(8 hours)
          enabled: ${self:custom.enabled.${opt:stage}, false}

  RentCafeV2SendProspectFunction:
    handler: src/modules/rentCafeV2/serverless/sendRentCafeV2Prospect.handler
    name: ${self:provider.stage}-rent-cafe-v2-send-prospect
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - RentCafeV2SendProspectQueue
              - Arn

  RentCafeV2ScheduleSGTFunction:
    handler: src/modules/rentCafeV2/serverless/sgt/scheduleSGT.handler
    name: ${self:provider.stage}-rent-cafe-v2-schedule-sgt
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - RentCafeV2ScheduleSGTQueue
              - Arn
  RentCafeV2RescheduleSGTFunction:
    handler: src/modules/rentCafeV2/serverless/sgt/rescheduleSGT.handler
    name: ${self:provider.stage}-rent-cafe-v2-reschedule-sgt
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - RentCafeV2RescheduleSGTQueue
              - Arn
  RentCafeV2CancelSGTFunction:
    handler: src/modules/rentCafeV2/serverless/sgt/cancelSGT.handler
    name: ${self:provider.stage}-rent-cafe-v2-cancel-sgt
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - RentCafeV2CancelSGTQueue
              - Arn
  RentCafeV2CompleteSGT:
    handler: src/modules/rentCafeV2/serverless/sgt/completeSGT.handler
    name: ${self:provider.stage}-rent-cafe-v2-complete-sgt
    reservedConcurrency: 5
    timeout: 900
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SESSION_SECRET: ${ssm:/${self:provider.stage}/SESSION_SECRET}
      HERE_API_KEY: ${ssm:/${self:provider.stage}/HERE_API_KEY}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_OPERATIONS_EMAIL: ${ssm:/${self:provider.stage}/PEEK_OPERATIONS_EMAIL}
      PARTNERS_API_SALT: ${ssm:/${self:provider.stage}/PARTNERS_API_SALT}
      SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SEND_VIRTUAL_TOUR_EVENT_TOPIC_ARN}
      DYNAMICS_VT_CONSOLIDATION_TIME: ${ssm:/${self:provider.stage}/DYNAMICS_VT_CONSOLIDATION_TIME}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      WELCOME_SITE_BASE_URL: ${ssm:/${self:provider.stage}/WELCOME_SITE_BASE_URL}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
      APARTMENT_DOT_COM_FILE_NAME: ${ssm:/${self:provider.stage}/APARTMENT_DOT_COM_FILE_NAME}
      APARTMENT_FTP_HOST: ${ssm:/${self:provider.stage}/APARTMENT_FTP_HOST}
      APARTMENT_FTP_PORT: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PORT}
      APARTMENT_FTP_USER: ${ssm:/${self:provider.stage}/APARTMENT_FTP_USER}
      APARTMENT_FTP_PASSWORD: ${ssm:/${self:provider.stage}/APARTMENT_FTP_PASSWORD}
      DASHBOARD_URL: ${ssm:/${self:provider.stage}/DASHBOARD_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      REPORTS_BUCKET: ${ssm:/${self:provider.stage}/REPORTS_BUCKET}
      AWS_S3_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_DOMAIN}
      AWS_S3_REPORT_DOMAIN: ${ssm:/${self:provider.stage}/AWS_S3_REPORT_DOMAIN}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_QUEUE_URL}
      DX_SYNC_ENTRATA_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_SYNC_ENTRATA_QUEUE_URL}
      YARDI_WORKER_EXEC_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_WORKER_EXEC_QUEUE_URL}
      YARDI_CREATE_SGT_EVENT_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_CREATE_SGT_EVENT_QUEUE_URL}
      SGT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/SGT_EVENT_TOPIC_ARN}
      PROSPECT_EVENT_TOPIC_ARN: ${ssm:/${self:provider.stage}/PROSPECT_EVENT_TOPIC_ARN}
      PROSPECT_SYNC_QUEUE: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_QUEUE}
      PROSPECT_SYNC_TOPIC: ${ssm:/${self:provider.stage}/PROSPECT_SYNC_TOPIC}
      SYNC_PROSPECT_EVENTS_TOPIC: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_TOPIC}
      FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL: ${ssm:/${self:provider.stage}/FUNNEL_PROSPECT_EVENT_WORKER_QUEUE_URL}
      SYNC_PROSPECT_EVENTS_QUEUE_URL: ${ssm:/${self:provider.stage}/SYNC_PROSPECT_EVENTS_QUEUE_URL}
      PROSPECTS_EVENTS_BUCKET: ${ssm:/${self:provider.stage}/PROSPECTS_EVENTS_BUCKET}
      MPLCONFIGDIR: ${ssm:/${self:provider.stage}/MPLCONFIGDIR}
      ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ENTRATA_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/YARDI_FIX_EXTERNAL_LINK_QUEUE_URL}
      YARDI_UPDATE_COMMENTS_QUEUE: ${ssm:/${self:provider.stage}/YARDI_UPDATE_COMMENTS_QUEUE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - RentCafeV2CompleteSGTQueue
              - Arn

  SyncCasaSummary:
    handler: src/modules/dataExchange/serverless/syncCasaSummary.handler
    name: ${self:provider.stage}-sync-casa-summary
    reservedConcurrency: 5
    environment:
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
    timeout: 900
    events:
      - sqs:
          batchSize: 10
          arn:
            Fn::GetAtt:
              - SyncCasaSummaryQueue
              - Arn
          functionResponseType: ReportBatchItemFailures

resources:
  Resources:
    SgtEventTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:provider.stage}-sgt-event-topic

    SendProspectEvent:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:provider.stage}-send-prospect-event

    SendVirtualTourEventTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:provider.stage}-send-virtual-tour-event-topic

    SendMailEventTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:provider.stage}-send-mail-event-topic

    ProspectSnsToQueueSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - EntrataSendProspectQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendProspectEvent
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - FunnelSendProspectQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendProspectEvent
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - AnyOneHomeProspectQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendProspectEvent
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - YardiSendProspectQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendProspectEvent
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - KnockSendProspectQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendProspectEvent
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - SendDynamicsProspectQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendProspectEvent
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - RentCafeV2SendProspectQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendProspectEvent
        Queues:
          - Ref: EntrataSendProspectQueue
          - Ref: FunnelSendProspectQueue
          - Ref: AnyOneHomeProspectQueue
          - Ref: YardiSendProspectQueue
          - Ref: KnockSendProspectQueue
          - Ref: SendDynamicsProspectQueue
          - Ref: RentCafeV2SendProspectQueue

    SgtSnsToQueueSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - EntrataScheduleSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - EntrataRescheduleSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - EntrataCancelSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - EntrataCompleteSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - FunnelScheduleSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - FunnelRescheduleSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - FunnelCancelSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - FunnelCompleteSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - YardiScheduleSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - YardiRescheduleSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - YardiCancelSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - YardiCompleteSgtTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - SendDynamicsAppointmentQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - UpdateDynamicsAppointmentQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - CompleteDynamicsAppointmentQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - CancelDynamicsAppointmentQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - RentCafeV2ScheduleSGTQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - RentCafeV2RescheduleSGTQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - RentCafeV2CompleteSGTQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - RentCafeV2CancelSGTQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SgtEventTopic

        Queues:
          - Ref: EntrataScheduleSgtTourQueue
          - Ref: EntrataRescheduleSgtTourQueue
          - Ref: EntrataCancelSgtTourQueue
          - Ref: EntrataCompleteSgtTourQueue
          - Ref: FunnelScheduleSgtTourQueue
          - Ref: FunnelRescheduleSgtTourQueue
          - Ref: FunnelCancelSgtTourQueue
          - Ref: FunnelCompleteSgtTourQueue
          - Ref: YardiScheduleSgtTourQueue
          - Ref: YardiRescheduleSgtTourQueue
          - Ref: YardiCancelSgtTourQueue
          - Ref: YardiCompleteSgtTourQueue
          - Ref: SendDynamicsAppointmentQueue
          - Ref: UpdateDynamicsAppointmentQueue
          - Ref: CompleteDynamicsAppointmentQueue
          - Ref: CancelDynamicsAppointmentQueue
          - Ref: RentCafeV2ScheduleSGTQueue
          - Ref: RentCafeV2RescheduleSGTQueue
          - Ref: RentCafeV2CompleteSGTQueue
          - Ref: RentCafeV2CancelSGTQueue

    VirtualToursSnsToQueueSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - SendDynamicsVirtualTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendVirtualTourEventTopic
        Queues:
          - Ref: SendDynamicsVirtualTourQueue

    SendMailSnsToQueueSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - KnockSendMailQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendMailEventTopic
        Queues:
          - Ref: KnockSendMailQueue

    SendDynamicsProspectQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-dynamics-prospect-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SendDynamicsProspectQueueDLQ
              - Arn
          maxReceiveCount: 3
    SendDynamicsProspectQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-dynamics-prospect-queue-dlq
    GsDynamicsSendProspectSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SendDynamicsProspectQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendProspectEvent
        FilterPolicy:
          service:
            - gsDynamics

    SendDynamicsAppointmentQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-gs-dynamics-appointment-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SendDynamicsAppointmentQueueDLQ
              - Arn
          maxReceiveCount: 3
    SendDynamicsAppointmentQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-gs-dynamics-appointment-queue-dlq
    GsDynamicsScheduleSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SendDynamicsAppointmentQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - gsDynamics
          sgtType:
            - SCHEDULE_TOUR

    UpdateDynamicsAppointmentQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-update-gs-dynamics-appointment-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - UpdateDynamicsAppointmentQueueDLQ
              - Arn
          maxReceiveCount: 3
    UpdateDynamicsAppointmentQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-update-gs-dynamics-appointment-queue-dlq
    GsDynamicsRescheduleSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - UpdateDynamicsAppointmentQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - gsDynamics
          sgtType:
            - RESCHEDULE_TOUR

    CompleteDynamicsAppointmentQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-complete-gs-dynamics-appointment-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - CompleteDynamicsAppointmentQueueDLQ
              - Arn
          maxReceiveCount: 3
    CompleteDynamicsAppointmentQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-complete-gs-dynamics-appointment-queue-dlq
    GsDynamicsCompleteSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - CompleteDynamicsAppointmentQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - gsDynamics
          sgtType:
            - COMPLETE_TOUR

    CancelDynamicsAppointmentQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-cancel-gs-dynamics-appointment-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - CancelDynamicsAppointmentQueueDLQ
              - Arn
          maxReceiveCount: 3
    CancelDynamicsAppointmentQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-cancel-gs-dynamics-appointment-queue-dlq
    GsDynamicsCancelSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - CancelDynamicsAppointmentQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - gsDynamics
          sgtType:
            - CANCEL_TOUR
    SendDynamicsVirtualTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-gs-dynamics-virtual-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SendDynamicsVirtualTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    SendDynamicsVirtualTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-gs-dynamics-virtual-tour-queue-dlq
    SendDynamicsVirtualTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SendDynamicsVirtualTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendVirtualTourEventTopic
        FilterPolicy:
          service:
            - gsDynamics

    EntrataSendProspectQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-entrata-send-prospect-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - EntrataSendProspectQueueDLQ
              - Arn
          maxReceiveCount: 3
    EntrataSendProspectQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-entrata-send-prospect-queue-dlq
    EntrataSendProspectSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - EntrataSendProspectQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendProspectEvent
        FilterPolicy:
          service:
            - entrata

    FunnelSendProspectQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-funnel-send-prospect-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - FunnelSendProspectQueueDLQ
              - Arn
          maxReceiveCount: 3
    FunnelSendProspectQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-funnel-send-prospect-queue-dlq
    FunnelSendProspectSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - FunnelSendProspectQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendProspectEvent
        FilterPolicy:
          service:
            - funnel

    AnyOneHomeProspectQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-send-prospect-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - AnyOneHomeProspectQueueDLQ
              - Arn
          maxReceiveCount: 3
    AnyOneHomeProspectQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-send-prospect-queue-dlq
    AnyOneHomeProspectSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - AnyOneHomeProspectQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendProspectEvent
        FilterPolicy:
          service:
            - anyoneHome

    YardiSendProspectQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-send-prospect-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - YardiSendProspectQueueDLQ
              - Arn
          maxReceiveCount: 3
    YardiSendProspectQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-send-prospect-queue-dlq
    YardiSendProspectSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - YardiSendProspectQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendProspectEvent
        FilterPolicy:
          service:
            - yardi

    KnockSendProspectQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-send-prospect-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - KnockSendProspectQueueDLQ
              - Arn
          maxReceiveCount: 3
    KnockSendProspectQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-send-prospect-queue-dlq
    KnockSendProspectSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - KnockSendProspectQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendProspectEvent
        FilterPolicy:
          service:
            - knock

    KnockSendMailQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-send-mail-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - KnockSendMailQueueDLQ
              - Arn
          maxReceiveCount: 3
    KnockSendMailQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-send-mail-queue-dlq
    KnockSendMailSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - KnockSendMailQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendMailEventTopic
        FilterPolicy:
          service:
            - knock

    FunnelScheduleSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-funnel-schedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - FunnelScheduleSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    FunnelScheduleSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-funnel-schedule-sgt-tour-queue-dlq
    FunnelScheduleSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - FunnelScheduleSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - funnel
          sgtType:
            - SCHEDULE_TOUR

    FunnelRescheduleSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-funnel-reschedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - FunnelRescheduleSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    FunnelRescheduleSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-funnel-reschedule-sgt-tour-queue-dlq
    FunnelRescheduleSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - FunnelRescheduleSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - funnel
          sgtType:
            - RESCHEDULE_TOUR

    FunnelCancelSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-funnel-cancel-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - FunnelCancelSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    FunnelCancelSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-funnel-cancel-sgt-tour-queue-dlq
    FunnelCancelSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - FunnelCancelSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - funnel
          sgtType:
            - CANCEL_TOUR

    FunnelCompleteSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-funnel-complete-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - FunnelCompleteSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    FunnelCompleteSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-funnel-complete-sgt-tour-queue-dlq
    FunnelCompleteSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - FunnelCompleteSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - funnel
          sgtType:
            - COMPLETE_TOUR
    EntrataScheduleSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-entrata-schedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - EntrataScheduleSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    EntrataScheduleSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-entrata-schedule-sgt-tour-queue-dlq
    EntrataScheduleSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - EntrataScheduleSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - entrata
          sgtType:
            - SCHEDULE_TOUR

    EntrataRescheduleSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-entrata-reschedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - EntrataRescheduleSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    EntrataRescheduleSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-entrata-reschedule-sgt-tour-queue-dlq
    EntrataRescheduleSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - EntrataRescheduleSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - entrata
          sgtType:
            - RESCHEDULE_TOUR

    EntrataCancelSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-entrata-cancel-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - EntrataCancelSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    EntrataCancelSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-entrata-cancel-sgt-tour-queue-dlq
    EntrataCancelSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - EntrataCancelSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - entrata
          sgtType:
            - CANCEL_TOUR

    EntrataCompleteSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-entrata-complete-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - EntrataCompleteSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    EntrataCompleteSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-entrata-complete-sgt-tour-queue-dlq
    EntrataCompleteSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - EntrataCompleteSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - entrata
          sgtType:
            - COMPLETE_TOUR

    YardiScheduleSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-schedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - YardiScheduleSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    YardiScheduleSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-schedule-sgt-tour-queue-dlq
    YardiCreateSgtEventQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-create-sgt-event-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - YardiCreateSgtEventQueueDLQ
              - Arn
          maxReceiveCount: 3
    YardiCreateSgtEventQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-create-sgt-event-queue-dlq
    YardiScheduleSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - YardiScheduleSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - yardi
          sgtType:
            - SCHEDULE_TOUR

    YardiRescheduleSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-reschedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - YardiRescheduleSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    YardiRescheduleSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-reschedule-sgt-tour-queue-dlq
    YardiRescheduleSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - YardiRescheduleSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - yardi
          sgtType:
            - RESCHEDULE_TOUR

    YardiCancelSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-cancel-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - YardiCancelSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    YardiCancelSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-cancel-sgt-tour-queue-dlq
    YardiCancelSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - YardiCancelSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - yardi
          sgtType:
            - CANCEL_TOUR

    YardiCompleteSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-complete-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - YardiCompleteSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    YardiCompleteSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-yardi-complete-sgt-tour-queue-dlq
    YardiCompleteSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - YardiCompleteSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - yardi
          sgtType:
            - COMPLETE_TOUR
    CreateSpaceAsyncQueueFifo:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-create-space-async-queue.fifo
        FifoQueue: true
        ContentBasedDeduplication: true
        VisibilityTimeout: 60
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - CreateSpaceAsyncQueueFifoDLQ
              - Arn
          maxReceiveCount: 3
    CreateSpaceAsyncQueueFifoDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-create-space-async-queue-dlq.fifo
        FifoQueue: true
        ContentBasedDeduplication: true

    RentCafeV2SendProspectQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-rent-cafe-v2-send-prospect-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - RentCafeV2SendProspectQueueDLQ
              - Arn
          maxReceiveCount: 3
    RentCafeV2SendProspectQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-rent-cafe-v2-send-prospect-queue-dlq
    RentCafeV2SendProspectSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - RentCafeV2SendProspectQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendProspectEvent
        FilterPolicy:
          service:
            - rentCafeV2

    RentCafeV2ScheduleSGTQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-rent-cafe-v2-schedule-sgt-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - RentCafeV2ScheduleSGTQueueDLQ
              - Arn
          maxReceiveCount: 3
    RentCafeV2ScheduleSGTQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-rent-cafe-v2-schedule-sgt-queue-dlq
    RentCafeV2ScheduleSGTSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - RentCafeV2ScheduleSGTQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - rentCafeV2
          sgtType:
            - SCHEDULE_TOUR

    RentCafeV2RescheduleSGTQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-rent-cafe-v2-reschedule-sgt-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - RentCafeV2RescheduleSGTQueueDLQ
              - Arn
          maxReceiveCount: 3
    RentCafeV2RescheduleSGTQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-rent-cafe-v2-reschedule-sgt-queue-dlq
    RentCafeV2RescheduleSGTSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - RentCafeV2RescheduleSGTQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - rentCafeV2
          sgtType:
            - RESCHEDULE_TOUR

    RentCafeV2CancelSGTQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-rent-cafe-v2-cancel-sgt-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - RentCafeV2CancelSGTQueueDLQ
              - Arn
          maxReceiveCount: 3
    RentCafeV2CancelSGTQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-rent-cafe-v2-cancel-sgt-queue-dlq
    RentCafeV2CancelSGTSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - RentCafeV2CancelSGTQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - rentCafeV2
          sgtType:
            - CANCEL_TOUR

    RentCafeV2CompleteSGTQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-rent-cafe-v2-complete-sgt-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - RentCafeV2CompleteSGTQueueDLQ
              - Arn
          maxReceiveCount: 3
    RentCafeV2CompleteSGTQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-rent-cafe-v2-complete-sgt-queue-dlq
    RentCafeV2CompleteSGTSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - RentCafeV2CompleteSGTQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SgtEventTopic
        FilterPolicy:
          service:
            - rentCafeV2
          sgtType:
            - COMPLETE_TOUR

    SyncCasaSummaryQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-casa-summary-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SyncCasaSummaryQueueDLQ
              - Arn
          maxReceiveCount: 3
    SyncCasaSummaryQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-casa-summary-queue-dlq

package:
  individually: true

plugins:
  - serverless-esbuild
  - ./serverless/sentry-log-error.js
