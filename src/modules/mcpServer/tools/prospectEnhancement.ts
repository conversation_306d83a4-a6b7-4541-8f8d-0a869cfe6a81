import { Tool } from '@modelcontextprotocol/sdk/types.js'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Tools } from '../types/tools'
import {
  McpProspectEnhancementInSchema,
  McpProspectEnhancementIn,
  ProspectEnhancementOut
} from '../schemas'
import { getProspectEnhancement } from '../services'

export const prospectEnhancementTools: Tool[] = [
  {
    name: Tools.ProspectEnhancement,
    description:
      'Retrieve comprehensive data for a prospect including their intelligence score, activities, room views, ' +
      'engagement metrics, preferences (ideal bedrooms, prices, floor plans), and behavioral insights. ' +
      'This tool aggregates data from multiple sources to provide a complete profile of the prospect. ' +
      'Optionally filter prospect events by externalId.',
    inputSchema: zodToJsonSchema(
      McpProspectEnhancementInSchema,
      'McpProspectEnhancementInSchema'
    ).definitions.McpProspectEnhancementInSchema as any,
    annotations: {
      readOnlyHint: true
    }
  }
]

export type ProspectEnhancementToolFunctions = {
  [Tools.ProspectEnhancement]: (
    args: McpProspectEnhancementIn
  ) => Promise<ProspectEnhancementOut>
}

export const prospectEnhancementToolFunctions: ProspectEnhancementToolFunctions =
  {
    [Tools.ProspectEnhancement]: getProspectEnhancement
  }
