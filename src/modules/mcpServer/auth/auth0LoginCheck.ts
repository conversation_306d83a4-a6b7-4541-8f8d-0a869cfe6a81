import { Router } from 'express'
import { auth0Login<PERSON>heck } from './utils'

export const auth0Routes = Router()

auth0Routes.post('/auth0-login-check', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    const result = await auth0Login<PERSON>heck(authHeader)
    res.status(200).json(result)
  } catch (error) {
    res.status(error.status || 500).json({
      error: error.error || error.payload?.error || 'internal_error',
      errorDescription:
        error.errorDescription ||
        error.payload?.errorDescription ||
        error.message ||
        'An unexpected error occurred'
    })
  }
})

export default auth0Routes
