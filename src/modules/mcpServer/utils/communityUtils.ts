import { Community } from '@modules/communities/types/community'
import { McpCommunityOut } from '../schemas/mcpCommunityOut'
import { McpAmenityOut } from '../schemas/mcpAmenityOut'
import { Space } from '@modules/communities/types/space'
import {
  virtualTourUrl,
  welcomeSiteUrl,
  bookATourUrl
} from '@modules/communities/helpers/tours'

export const communityToMcpCommunityOut = (
  community: Community,
  amenities: McpAmenityOut[]
): McpCommunityOut => {
  const pointsOfInterestNames: string[] = []

  if (community.pointOfInterest) {
    if (community.pointOfInterest.nearbyTransit?.[0]?.name) {
      pointsOfInterestNames.push(
        ...community.pointOfInterest.nearbyTransit.map((place) => place.name)
      )
    }

    if (community.pointOfInterest?.nearbyGrocery?.name) {
      pointsOfInterestNames.push(community.pointOfInterest.nearbyGrocery.name)
    }

    if (community.pointOfInterest?.nearbyGym?.name) {
      pointsOfInterestNames.push(community.pointOfInterest.nearbyGym.name)
    }

    if (community.pointOfInterest?.nearbyPark?.name) {
      pointsOfInterestNames.push(community.pointOfInterest.nearbyPark.name)
    }
  }

  return {
    _id: community._id.toString(),
    name: community.name,
    amenitiesSpaces: amenities,
    amenities: community.amenities,
    address: community.address?.street2,
    bookATourUrl: bookATourUrl(community._id.toString()),
    welcomeSiteUrl: welcomeSiteUrl(community._id.toString()),
    applyUrl: community.communityInfo?.applyUrl || '',
    pointsOfInterest: pointsOfInterestNames
  }
}

export const amenityToMcpAmenityOut = (amenity: Space): McpAmenityOut => {
  return {
    _id: amenity._id.toString(),
    name: amenity.unit,
    spaceCategory: amenity.spaceCategory,
    spaceFunction: amenity.spaceFunction,
    spaceDetail: amenity.spaceDetail,
    virtualTourUrl: amenity.token ? virtualTourUrl(amenity.token) : undefined
  }
}

export const communityMcpOutProjection = {
  _id: 1,
  name: 1,
  amenities: 1,
  metroArea: 1,
  address: 1,
  pointOfInterest: 1,
  communityInfo: 1
}

export const amenityMcpOutProjection = {
  _id: 1,
  unit: 1,
  token: 1,
  community: 1,
  spaceCategory: 1,
  spaceFunction: 1,
  spaceDetail: 1
}
