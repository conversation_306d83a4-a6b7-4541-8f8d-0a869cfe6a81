import { ProspectEnhancementOut } from '../../schemas'
import { logWarn } from '@core/log'
import { findProspectIntelligenceById } from '@modules/analytics/services/prospectIntelligence'
import {
  getRoomsViewed,
  getTotalSessions,
  getTotalEngagement,
  getTotalSpaces,
  getIdealBedrooms,
  getIdealPrices,
  getIdealFloorPlans,
  getHotnessLeadScore,
  getProspectActivities
} from '@modules/analytics/services/prospectIntelligence'
import { getProspectEvents } from '@modules/prospects/services/prospectEvents'
import { ObjectId } from 'mongodb'

const location = 'mcp-prospect-enhancement-fetch-functions'

export async function fetchProspectIntelligence(
  prospectId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const pi = await findProspectIntelligenceById(prospectId)
    if (pi) {
      result.prospectIntelligence = pi
    }
  } catch (error) {
    logWarn(location, 'Failed to fetch prospect intelligence', {
      prospectId,
      error
    })
    result.errors?.push({
      endpoint: 'prospectIntelligence',
      error: error.message || 'Failed to fetch prospect intelligence'
    })
  }
}

export async function fetchProspectActivities(
  prospectId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const data = await getProspectActivities({
      prospectId: new ObjectId(prospectId)
    })
    result.activities = data.data
  } catch (error) {
    logWarn(location, 'Failed to fetch prospect activities', {
      prospectId: prospectId,
      error
    })
    result.errors?.push({
      endpoint: 'activities',
      error: error.message || 'Failed to fetch activities'
    })
  }
}

export async function fetchProspectRoomViews(
  prospectId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const data = await getRoomsViewed({
      prospectId: new ObjectId(prospectId)
    })
    result.roomsViewed = data
  } catch (error) {
    logWarn(location, 'Failed to fetch room views', {
      prospectId: prospectId,
      error
    })
    result.errors?.push({
      endpoint: 'roomsViewed',
      error: error.message || 'Failed to fetch room views'
    })
  }
}

export async function fetchProspectTotalSessions(
  prospectId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const data = await getTotalSessions({
      prospectId: new ObjectId(prospectId)
    })
    result.totalSessions = data.total
  } catch (error) {
    logWarn(location, 'Failed to fetch total sessions', {
      prospectId: prospectId,
      error
    })
    result.errors?.push({
      endpoint: 'totalSessions',
      error: error.message || 'Failed to fetch total sessions'
    })
  }
}

export async function fetchProspectTotalEngagement(
  prospectId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const data = await getTotalEngagement({
      prospectId: new ObjectId(prospectId)
    })
    result.totalEngagement = data.total
  } catch (error) {
    logWarn(location, 'Failed to fetch total engagement', {
      prospectId: prospectId,
      error
    })
    result.errors?.push({
      endpoint: 'totalEngagement',
      error: error.message || 'Failed to fetch total engagement'
    })
  }
}

export async function fetchProspectTotalSpacesViews(
  prospectId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const data = await getTotalSpaces({
      prospectId: new ObjectId(prospectId)
    })
    result.totalSpacesViews = data.total
  } catch (error) {
    logWarn(location, 'Failed to fetch total spaces views', {
      prospectId: prospectId,
      error
    })
    result.errors?.push({
      endpoint: 'totalSpacesViews',
      error: error.message || 'Failed to fetch total spaces views'
    })
  }
}

export async function fetchProspectIdealBedrooms(
  prospectId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const data = await getIdealBedrooms({
      prospectId: new ObjectId(prospectId)
    })
    result.idealBedrooms = data
  } catch (error) {
    logWarn(location, 'Failed to fetch ideal bedrooms', {
      prospectId: prospectId,
      error
    })
    result.errors?.push({
      endpoint: 'idealBedrooms',
      error: error.message || 'Failed to fetch ideal bedrooms'
    })
  }
}

export async function fetchProspectIdealPrices(
  prospectId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const data = await getIdealPrices({
      prospectId: new ObjectId(prospectId)
    })
    result.idealPrices = data
  } catch (error) {
    logWarn(location, 'Failed to fetch ideal prices', {
      prospectId: prospectId,
      error
    })
    result.errors?.push({
      endpoint: 'idealPrices',
      error: error.message || 'Failed to fetch ideal prices'
    })
  }
}

export async function fetchProspectIdealFloorPlans(
  prospectId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const data = await getIdealFloorPlans({
      prospectId: new ObjectId(prospectId)
    })
    result.idealFloorPlans = data
  } catch (error) {
    logWarn(location, 'Failed to fetch ideal floor plans', {
      prospectId: prospectId,
      error
    })
    result.errors?.push({
      endpoint: 'idealFloorPlans',
      error: error.message || 'Failed to fetch ideal floor plans'
    })
  }
}

export async function fetchProspectScore(
  prospectId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const data = await getHotnessLeadScore({
      prospectId: new ObjectId(prospectId)
    })

    result.score = {
      piScore: data.piScore,
      piStatus: data.piStatus
    }
  } catch (error) {
    logWarn(location, 'Failed to fetch prospect score', {
      prospectId: prospectId,
      error
    })
    result.errors?.push({
      endpoint: 'score',
      error: error.message || 'Failed to fetch prospect score'
    })
  }
}

export async function fetchProspectEvents(
  prospectId: string,
  externalId: string,
  result: ProspectEnhancementOut
): Promise<void> {
  try {
    const data = await getProspectEvents(prospectId, externalId)

    result.prospectEvents = {
      total: data.total,
      events: data.events
    }
  } catch (error) {
    logWarn(location, 'Failed to fetch prospect events', {
      prospectId: prospectId,
      externalId: externalId,
      error
    })
    result.errors?.push({
      endpoint: 'prospectevents',
      error: error.message || 'Failed to fetch prospect events'
    })
  }
}
