import { ObjectId } from 'mongoose'
import { Id } from './id'
import { Space } from './space'

export interface ExternalLink {
  _id?: Id | string
  service: ExternalLinkService
  externalId: string
  externalName?: ExternalLinkName
  objectId: ObjectId | string
  collectionPath: ExternalLinkCollection
  communityId?: ObjectId | string
  deletedAt?: Date
  createdAt?: Date
  updatedAt?: Date
  deletedBy?: string
  updatedBy?: string
}

export type ExternalLinkWithSpace = ExternalLink & {
  space: Pick<Space, '_id' | 'unit' | 'isComplete' | 'building' | 'community'>
}

export type ExternalLinkSpaceX = Pick<
  ExternalLink,
  'externalId' | 'externalName' | 'service'
>

export enum ExternalLinkService {
  Entrata = 'entrata',
  Yardi = 'yardi',
  Funnel = 'funnel',
  RealPage = 'realpage',
  AnyoneHome = 'anyoneHome',
  Knock = 'knock',
  RentCafe = 'rentCafe',
  RentCafeV2 = 'rentCafeV2',
  ApartmentsDotCom = 'apartmentsDotCom',
  GsDynamics = 'gsDynamics',
  Engrain = 'engrain',
  Geokey = 'geokey',
  Latch = 'latch',
  BozzutoMits = 'bozzutoMits',
  Cortland = 'cortland',
  Seam = 'seam',
  Resman = 'resman'
}

export enum ExternalLinkCollection {
  AccessDevices = 'accessDevices',
  Communities = 'communities',
  Spaces = 'spaces',
  Buildings = 'buildings',
  Prospects = 'prospects',
  Appointments = 'appointments',
  Walkabouts = 'walkabouts',
  Leases = 'leases',
  OutboundLink = 'outboundLink'
}

export enum ExternalLinkName {
  EventId = 'EventID',
  ThirdPartyId = 'ThirdPartyID',
  CustomerId = 'CustomerID',
  UnitSpaceId = 'unitSpaceId',
  UnitId = 'unitId',
  UnitDotId = 'unit.id',
  ApplicantId = 'applicantId',
  ApplicationId = 'applicationId',
  RentCafeProspectId = 'rentcafeProspectId',
  VoyProspectId = 'voyProspectId',
  TourId = 'TourId',
  OutboundLink = 'outboundLink',
  EngrainUnit = 'engrainUnit',
  SeamConnectWebviewId = 'seamConnectWebviewId',
  EngrainSynced = 'engrainSynced',
  EngrainSyncedByFloorplan = 'engrainSyncedByFloorplan'
}

export type CreateExternalLink = Pick<
  ExternalLink,
  | 'objectId'
  | 'service'
  | 'externalId'
  | 'collectionPath'
  | 'externalName'
  | 'communityId'
>
