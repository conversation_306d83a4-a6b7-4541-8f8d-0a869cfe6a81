import { logDebug } from '@core/log'
import { getEnvVariable } from '@core/util'
import {
  UnitAvailabilityData,
  UnitTypesData,
  PropertiesData,
  Property,
  ResmanError
} from '@modules/resman/types/resman'
import axios from 'axios'

const location = 'resman-gateway'

const resmanGateway = axios.create({
  baseURL: getEnvVariable('PEEK_RESMAN_API_URL'),
  auth: {
    username: getEnvVariable('PEEK_RESMAN_INTEGRATION_PARTNER_ID'),
    password: getEnvVariable('PEEK_RESMAN_API_KEY')
  },
  headers: {
    'ResMan-Account-Id': getEnvVariable('PEEK_RESMAN_ACCOUNT_ID')
  }
})

export const getUnitAvailability = async (propertyId: string) => {
  logDebug(location, 'Fetching unit availabilities', { propertyId })
  try {
    const response = await resmanGateway.get(`/Units/Availability`, {
      params: {
        propertyId
      }
    })
    logDebug(location, 'Received unit availabilities', { response })
    return response.data as unknown as UnitAvailabilityData
  } catch (error) {
    throw new ResmanError(
      `Failed to get unit availabilities: ${(error as Error).message}`,
      propertyId,
      '/Units/Availability',
      error
    )
  }
}

export const getUnitTypes = async (
  propertyId: string
): Promise<UnitTypesData> => {
  logDebug(location, 'Fetching unit types', { propertyId })
  try {
    const response = await resmanGateway.get(`/UnitTypes`, {
      params: {
        propertyId
      }
    })
    logDebug(location, 'Received unit types', { response })
    return response.data as unknown as UnitTypesData
  } catch (error) {
    throw new ResmanError(
      `Failed to get unit types: ${(error as Error).message}`,
      propertyId,
      '/UnitTypes',
      error
    )
  }
}

export const getProperties = async (
  propertyId?: string
): Promise<PropertiesData> => {
  logDebug(location, 'Fetching properties', { propertyId })
  try {
    const response = await resmanGateway.get('/Properties', {
      params: propertyId ? { propertyId } : {}
    })
    logDebug(location, 'Received properties', { response })
    if (propertyId) {
      response.data.properties = response.data.properties.filter(
        (property: Property) => property.propertyId === propertyId
      )
    }
    return response.data as unknown as PropertiesData
  } catch (error) {
    throw new ResmanError(
      `Failed to get properties: ${(error as Error).message}`,
      propertyId,
      '/Properties',
      error
    )
  }
}
