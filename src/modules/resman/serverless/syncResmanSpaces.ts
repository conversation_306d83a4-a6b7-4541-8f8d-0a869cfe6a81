import { SQSEvent } from 'aws-lambda'
import { syncSpaces } from '@modules/resman/services/syncResmanSpaces'
import { logError, logInfo } from '@core/log'

const location = 'sync-resman-spaces-serverless'

export const handler = async (event: SQSEvent) => {
  const batchItemFailures = []
  for (const record of event.Records) {
    try {
      const body = JSON.parse(record.body)
      const { dxSettingId } = body
      logInfo(location, 'Syncing resman spaces', { dxSettingId })
      await syncSpaces(dxSettingId)
    } catch (error) {
      logError(location, 'Error syncing resman spaces', {
        error,
        body: record.body
      })
      batchItemFailures.push({ itemIdentifier: record.messageId })
    }
  }
  return { batchItemFailures }
}
