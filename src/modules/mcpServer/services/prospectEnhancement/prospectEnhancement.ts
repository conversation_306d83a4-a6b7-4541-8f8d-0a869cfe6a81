import { McpProspectEnhancementIn, ProspectEnhancementOut } from '../../schemas'
import { logDebug, logWarn } from '@core/log'
import { findProspectById } from '@modules/prospects/services/prospect'
import { McpError, ErrorCode } from '@modelcontextprotocol/sdk/types.js'
import {
  fetchProspectIntelligence,
  fetchProspectActivities,
  fetchProspectRoomViews,
  fetchProspectTotalSessions,
  fetchProspectTotalEngagement,
  fetchProspectTotalSpacesViews,
  fetchProspectIdealBedrooms,
  fetchProspectIdealPrices,
  fetchProspectIdealFloorPlans,
  fetchProspectScore,
  fetchProspectEvents
} from './fetchFunctions'

const location = 'mcp-prospect-enhancement-service'

export const getProspectEnhancement = async (
  input: McpProspectEnhancementIn
): Promise<ProspectEnhancementOut> => {
  logDebug(location, 'getProspectEnhancement called', { input })

  const result: ProspectEnhancementOut = {
    errors: []
  }

  const prospectId = input.prospectId
  const externalId = input.externalId

  let prospect = undefined
  try {
    prospect = await findProspectById(
      prospectId,
      '_id email firstName lastName phone communityId'
    )
    if (!prospect) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Prospect with ID ${prospectId} not found`
      )
    }

    result.prospect = prospect
  } catch (error) {
    logWarn(location, 'Failed to fetch prospect', { prospectId, error })

    if (error instanceof McpError) {
      throw error
    }

    throw new McpError(
      ErrorCode.InternalError,
      `Failed to fetch prospect: ${error.message || 'Unknown error'}`
    )
  }

  const promises = [
    fetchProspectIntelligence(prospectId, result),
    fetchProspectActivities(prospectId, result),
    fetchProspectRoomViews(prospectId, result),
    fetchProspectTotalSessions(prospectId, result),
    fetchProspectTotalEngagement(prospectId, result),
    fetchProspectTotalSpacesViews(prospectId, result),
    fetchProspectIdealBedrooms(prospectId, result),
    fetchProspectIdealPrices(prospectId, result),
    fetchProspectIdealFloorPlans(prospectId, result),
    fetchProspectScore(prospectId, result),
    fetchProspectEvents(prospectId, externalId, result)
  ]

  await Promise.allSettled(promises)

  const criticalErrors =
    result.errors?.filter(
      (error) =>
        error.endpoint === 'prospect' ||
        error.error.includes('not found') ||
        error.error.includes('access denied')
    ) || []

  if (criticalErrors.length > 0) {
    const errorMessage = criticalErrors
      .map((e) => `${e.endpoint}: ${e.error}`)
      .join('; ')
    throw new McpError(ErrorCode.InvalidRequest, errorMessage)
  }

  if (result.errors?.length === 0) {
    delete result.errors
  }

  logDebug(location, 'getProspectEnhancement completed', {
    prospectId,
    externalId,
    hasErrors: !!result.errors,
    hasProspectEvents: !!result.prospectEvents
  })

  return result
}

export default getProspectEnhancement
