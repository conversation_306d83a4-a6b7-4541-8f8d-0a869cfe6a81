jest.mock('connect-mongo', () => {
  return {
    create: jest.fn()
  }
})

jest.mock('@aws-sdk/client-secrets-manager', () => {
  return {
    SecretsManager: jest.fn().mockReturnValue({
      listSecrets: jest.fn().mockResolvedValue({
        SecretList: [
          {
            ARN: 'arn:aws:secretsmanager:us-east-1:123456789012:secret:example-secret'
          }
        ]
      }),
      getSecretValue: jest.fn().mockResolvedValue({ SecretString: '{}' })
    })
  }
})

jest.mock('@aws-sdk/client-sqs', () => {
  return {
    SQSClient: jest.fn().mockReturnValue({
      send: jest.fn().mockResolvedValue({})
    }),
    SendMessageCommand: jest.fn().mockResolvedValue({})
  }
})

jest.mock('@aws-sdk/client-sts', () => {
  return {
    STSClient: jest.fn().mockReturnValue({
      send: jest.fn().mockResolvedValue({})
    }),
    GetCallerIdentityCommand: jest.fn().mockResolvedValue({})
  }
})

jest.mock('@aws-sdk/client-sns', () => {
  return {
    SNSClient: jest.fn().mockReturnValue({
      send: jest.fn().mockResolvedValue({})
    }),
    PublishCommand: jest.fn().mockResolvedValue({})
  }
})

jest.mock('@aws-sdk/client-s3', () => {
  return {
    S3Client: jest.fn().mockReturnValue({
      send: jest.fn().mockResolvedValue({})
    }),
    HeadObjectCommand: jest.fn().mockResolvedValue({}),
    GetObjectCommand: jest.fn().mockResolvedValue({}),
    PutObjectCommand: jest.fn().mockResolvedValue({})
  }
})

jest.mock('prettier', () => {
  return {
    format: jest.fn().mockImplementation((code: string) => {
      return code
    })
  }
})
