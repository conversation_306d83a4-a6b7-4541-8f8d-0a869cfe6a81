import * as repo from '../repositories/prospectEvents'
import { ProspectEvent } from '../types/prospectEvent'
import { logWarn } from '@core/log'

const location = 'prospect-events-service'

export interface ProspectEventsResponse {
  total: number
  events: ProspectEvent[]
}

export const getProspectEvents = async (
  prospectId: string,
  externalId: string
): Promise<ProspectEventsResponse> => {
  try {
    const query = {
      prospectId,
      externalId
    }

    const [events, total] = await Promise.all([
      repo.findProspectEvents(query),
      repo.countProspectEvents(query)
    ])

    return {
      total,
      events
    }
  } catch (error) {
    logWarn(location, 'Failed to get prospect events', {
      prospectId,
      externalId,
      error
    })
    throw error
  }
}
