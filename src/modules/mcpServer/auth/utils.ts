import jwt from 'jsonwebtoken'
import jwksClient from 'jwks-rsa'
import { logWarn } from '@core/log'
import { AuthUser } from '@modules/users/types/user'
import { findUserByEmail } from '@modules/users/services/user'
import { JwtHeader, JwtPayload, SigningKeyCallback } from 'jsonwebtoken'
import ForbiddenError from '@core/errors/forbiddenError'
import UnauthorizedError from '@core/errors/unauthorizedError'
import { getEnvVariable } from '@core/util'
const MCP_POST_LOGIN_SECRET = process.env.MCP_POST_LOGIN_SECRET!
const AUTH0_DOMAIN = process.env.AUTH0_DOMAIN
const MCP_SERVER_URL = `${process.env.BASE_URL}/mcp`

const client = jwksClient({
  jwksUri: `https://${AUTH0_DOMAIN}/.well-known/jwks.json`,
  cache: true,
  rateLimit: true,
  cacheMaxAge: 600000
})

const getSigningKey = (header: JwtHeader, callback: SigningKeyCallback) => {
  if (!header.kid) {
    return callback(new Error('Missing kid in header'), undefined)
  }

  client.getSigningKey(header.kid, function (err, key) {
    if (err) return callback(err, undefined)
    const signingKey = key.getPublicKey()
    callback(null, signingKey)
  })
}

export const verifyAuth0Token = async (token: string): Promise<JwtPayload> => {
  const decoded = jwt.decode(token, { complete: true }) as any

  if (!decoded) {
    throw new Error('Invalid token format')
  }

  return new Promise<JwtPayload>((resolve, reject) => {
    jwt.verify(
      token,
      getSigningKey,
      {
        algorithms: ['RS256'],
        audience: [MCP_SERVER_URL]
      },
      (err, decoded) => {
        if (err) {
          jwt.verify(
            token,
            getSigningKey,
            {
              algorithms: ['RS256']
            },
            (err2, decoded2) => {
              if (err2) {
                reject(err)
              } else {
                resolve(decoded2 as JwtPayload)
              }
            }
          )
        } else {
          resolve(decoded as JwtPayload)
        }
      }
    )
  })
}

export const extractUserContext = (
  token: JwtPayload
): Omit<AuthUser, '_id'> & {
  userId: Pick<AuthUser, '_id'>
} => {
  const context = {
    userId: token['userId'],
    communities: token['communities'],
    organizations: token['organizations'],
    allow: token['allow'],
    deny: token['deny'],
    role: token['role']
  }

  return context
}

export const extractResourceIdsFromAllow = (
  permissions: string[] | undefined,
  resource: 'communities' | 'organizations'
): string[] => {
  if (!permissions || permissions.length === 0) return []
  const ids = new Set<string>()
  for (const permission of permissions) {
    if (!permission || typeof permission !== 'string') continue
    const [left] = permission.split('::')
    if (!left) continue
    const parts = left.split(':')
    if (parts.length >= 3 && parts[0] === 'peek' && parts[1] === resource) {
      const resourceId = parts[2]
      if (resourceId && resourceId !== '*') ids.add(resourceId)
    }
  }
  return Array.from(ids)
}

export async function auth0LoginCheck(authHeader) {
  if (!authHeader?.startsWith('Bearer ')) {
    throw new UnauthorizedError('Missing or invalid Authorization header', {
      error: 'invalid_request',
      errorDescription: 'Missing or invalid Authorization header'
    })
  }

  const token = authHeader.split(' ')[1]

  try {
    const payload = jwt.verify(token, MCP_POST_LOGIN_SECRET, {
      algorithms: ['HS256']
    }) as { email: string; purpose: string }

    if (payload.purpose !== 'peek-api-auth-check') {
      throw new ForbiddenError('Invalid token purpose', {
        error: 'invalid_token',
        errorDescription: 'Invalid token purpose'
      })
    }

    const user = await findUserByEmail(payload.email)

    if (!user || !user.status || user.status !== 'active') {
      throw new UnauthorizedError('User not found or inactive', {
        error: 'invalid_token',
        errorDescription: 'User not found or inactive'
      })
    }

    return {
      access_token: token,
      email: user.email,
      allow: user.allow,
      deny: user.deny,
      userId: user._id.toString(),
      communities: user.communities,
      organizations: user.organizations,
      role: user.role
    }
  } catch (err) {
    logWarn('auth0-login', 'Auth0 verification failed', { error: err })

    if (err.name === 'TokenExpiredError') {
      throw new UnauthorizedError('Token has expired', {
        error: 'invalid_token',
        errorDescription: 'Token has expired'
      })
    }

    if (err.status) {
      throw err
    }

    throw new UnauthorizedError('Invalid or expired token', {
      error: 'invalid_token',
      errorDescription: 'Invalid or expired token'
    })
  }
}

export const isM2M = (decoded: JwtPayload) => {
  return (
    (decoded as any)['gty'] === 'client-credentials' ||
    (typeof (decoded as any).sub === 'string' &&
      (decoded as any).sub.endsWith('@clients'))
  )
}

export const handleM2MToken = (decoded: JwtPayload) => {
  const envClientId = getEnvVariable('M2M_CLIENT_ID')

  let clientId = (decoded as any).azp || (decoded as any).client_id
  const sub: string | undefined = (decoded as any).sub
  if (!clientId && typeof sub === 'string') {
    if (sub.endsWith('@clients')) {
      clientId = sub.replace('@clients', '')
    }
  }

  if (!clientId) {
    throw new ForbiddenError('Missing client_id in token')
  }

  if (clientId !== envClientId) {
    throw new ForbiddenError('Invalid client_id in M2M request')
  }
}
