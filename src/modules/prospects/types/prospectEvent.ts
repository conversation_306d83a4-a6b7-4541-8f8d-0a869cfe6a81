export enum EventType {
  Message = 'message',
  Call = 'call',
  ProfileChange = 'profile.change',
  Application = 'application',
  Lease = 'lease',
  Show = 'show',
  MoveIn = 'move.in',
  Cancel = 'cancel',
  Transfer = 'transfer',
  Appointment = 'appointment',
  WalkIn = 'walk.in',
  Webservice = 'webservice',
  Other = 'other'
}

export interface ProspectEventPatch {
  op: 'replaced' | 'added' | 'removed' | 'created'
  path: string
  rawPath: string
  old?: any
  new?: any
}

export interface ProspectEventSource {
  system: string
  endpoint: string
  propertyId?: string
  agentName?: string
  rawRef?: string
}

export interface ProspectEventProspect {
  _id: string
  externalIds?: Record<string, string>
}

export interface BaseProspectEventPayload {
  eventReasons?: string[]
  comments?: string
}

export interface MessageEventPayload extends BaseProspectEventPayload {
  channel: 'email' | 'sms' | 'chat' | 'whatsapp' | 'other'
  direction: 'received' | 'sent' | 'unknown'
  body: string | null
  subject: string | null
  from: string | null
  to: string[] | null
  externalMessageId: string | null
  attachments: string[] | null
  comments: string
  eventReasons: string[]
}

export interface CallEventPayload extends BaseProspectEventPayload {
  direction: 'inbound' | 'outbound' | 'unknown'
  from: string | null
  to: string | null
  comments?: string
}

export interface ApplicationEventPayload extends BaseProspectEventPayload {
  applicationStatus: 'approved' | 'denied' | 'cancelled' | 'reapplied' | 'other'
  applicationType: 'lease' | 'movein' | 'other'
}

export interface LeaseEventPayload extends BaseProspectEventPayload {
  leaseStatus: 'signed' | 'sent' | 'other'
  leaseType: 'lease' | 'movein' | 'other'
}

export interface ProfileChangeEventPayload extends BaseProspectEventPayload {
  patches: ProspectEventPatch[]
}

export interface ProspectEventPayload {
  patches: ProspectEventPatch[]
}

export interface ProspectEvent {
  _id: any
  prospectId: any
  externalId: string
  extractionId: string
  type: string
  occurredAt: Date
  receivedAt: Date
  prospect: ProspectEventProspect
  source: ProspectEventSource
  eventReasons: string[]
  payload: ProspectEventPayload
  createdAt?: Date
  updatedAt?: Date
}
