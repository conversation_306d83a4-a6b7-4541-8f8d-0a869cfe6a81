import { findAllDxSettings } from '@modules/communities/services/dxSetting'
import { logInfo } from '@core/log'
import { publish } from '@core/sqs'
import { getEnvVariable } from '@core/util'
import { connectDb } from '@core/db'
import { DXSettingServices } from '@modules/communities/types/dxSetting'

const location = 'sync-resman-properties-service'

export const syncResmanProperties = async () => {
  await connectDb()

  const dxSettings = await findAllDxSettings({
    'dataExchange.syncPms': true,
    service: DXSettingServices.RESMAN,
    deletedAt: null
  })

  logInfo(
    location,
    `Found ${dxSettings.length} properties to sync: ${dxSettings
      .map((dxSetting) => dxSetting.resman.propertyId)
      .join(', ')}`
  )

  for (const dxSetting of dxSettings) {
    await publish(
      { dxSettingId: dxSetting._id.toString() },
      getEnvVariable('PEEK_RESMAN_SYNC_QUEUE_URL')
    )
  }
}
