import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express'
import jwt, { JwtHeader, JwtPayload, SigningKeyCallback } from 'jsonwebtoken'
import jwksClient from 'jwks-rsa'
import { logError } from '@core/log'

const auth0Domain = process.env.AUTH0_DOMAIN
const mcpServerUrl = process.env.MCP_SERVER_URL || 'http://localhost:4000/mcp'
const PEEK_API_JWT_SECRET = process.env.PEEK_API_JWT_SECRET

const client = jwksClient({
  jwksUri: `https://${auth0Domain}/.well-known/jwks.json`,
  cache: true,
  rateLimit: true,
  cacheMaxAge: 600000
})

const getSigningKey = (header: JwtHeader, callback: SigningKeyCallback) => {
  if (!header.kid) {
    return callback(new Error('Missing kid in header'), undefined)
  }
  client.getSigningKey(header.kid, function (err, key) {
    if (err) return callback(err, undefined)
    const signingKey = key.getPublicKey()
    callback(null, signingKey)
  })
}

const verifyJwtMultiStrategy = async (token: string): Promise<JwtPayload> => {
  const decoded = jwt.decode(token, { complete: true })

  if (!decoded) {
    throw new Error('Invalid token format')
  }

  if (decoded.header.alg === 'HS256' && PEEK_API_JWT_SECRET) {
    return new Promise<JwtPayload>((resolve, reject) => {
      jwt.verify(
        token,
        PEEK_API_JWT_SECRET,
        {
          algorithms: ['HS256']
        },
        (err, decoded) => {
          if (err) {
            reject(err)
          } else {
            resolve(decoded as JwtPayload)
          }
        }
      )
    })
  }

  if (decoded.header.alg === 'RS256') {
    return new Promise<JwtPayload>((resolve, reject) => {
      jwt.verify(
        token,
        getSigningKey,
        {
          algorithms: ['RS256'],
          audience: [
            mcpServerUrl,
            `https://${auth0Domain}/api/v2/`,
            `https://${auth0Domain}/userinfo`
          ],
          issuer: [`https://${auth0Domain}/`, `https://${auth0Domain}`]
        },
        (err, decoded) => {
          if (err) {
            jwt.verify(
              token,
              getSigningKey,
              {
                algorithms: ['RS256']
              },
              (err2, decoded2) => {
                if (err2) {
                  reject(err)
                } else {
                  resolve(decoded2 as JwtPayload)
                }
              }
            )
          } else {
            resolve(decoded as JwtPayload)
          }
        }
      )
    })
  }

  throw new Error(`Unsupported algorithm: ${decoded.header.alg}`)
}

export const authenticate = (): RequestHandler => {
  return async (req, res, next) => {
    try {
      const header = req.headers.authorization

      if (!header) {
        res.set(
          'WWW-Authenticate',
          `Bearer realm="${mcpServerUrl}", ` +
            `resource_metadata="/.well-known/oauth-protected-resource"`
        )

        return res.status(401).json({
          error: 'unauthorized',
          error_description: 'Missing Authorization header'
        })
      }

      const [type, token] = header.split(' ')
      if (type.toLowerCase() !== 'bearer' || !token) {
        res.set(
          'WWW-Authenticate',
          `Bearer realm="${mcpServerUrl}", ` +
            `error="invalid_request", ` +
            `error_description="Invalid Authorization header format, expected 'Bearer TOKEN'", ` +
            `resource_metadata="/.well-known/oauth-protected-resource"`
        )

        return res.status(401).json({
          error: 'invalid_request',
          error_description:
            "Invalid Authorization header format, expected 'Bearer TOKEN'"
        })
      }

      let decoded: JwtPayload

      try {
        decoded = await verifyJwtMultiStrategy(token.trim())
      } catch (verifyError) {
        logError('mcp-auth', 'Token verification failed', {
          error: verifyError.message,
          name: verifyError.name
        })

        if (verifyError.name === 'TokenExpiredError') {
          res.set(
            'WWW-Authenticate',
            `Bearer realm="${mcpServerUrl}", ` +
              `error="invalid_token", ` +
              `error_description="Token expired", ` +
              `resource_metadata="/.well-known/oauth-protected-resource"`
          )
          return res.status(401).json({
            error: 'invalid_token',
            error_description: 'Token expired'
          })
        }

        res.set(
          'WWW-Authenticate',
          `Bearer realm="${mcpServerUrl}", ` +
            `error="invalid_token", ` +
            `error_description="${verifyError.message}", ` +
            `resource_metadata="/.well-known/oauth-protected-resource"`
        )
        return res.status(401).json({
          error: 'invalid_token',
          error_description: verifyError.message || 'Invalid token'
        })
      }

      let hasValidScope = false

      if (decoded.scope) {
        const scopes =
          typeof decoded.scope === 'string'
            ? decoded.scope.split(' ')
            : decoded.scope

        hasValidScope =
          scopes.includes('mcp:tools') ||
          scopes.includes('read') ||
          scopes.includes('write') ||
          scopes.includes('*') ||
          (scopes.includes('openid') && scopes.includes('profile')) ||
          scopes.includes('email')

        if (!hasValidScope) {
          res.set(
            'WWW-Authenticate',
            `Bearer realm="${mcpServerUrl}", ` +
              `error="insufficient_scope", ` +
              `error_description="Token must have valid scopes (mcp:tools or OIDC scopes)", ` +
              `scope="mcp:tools", ` +
              `resource_metadata="/.well-known/oauth-protected-resource"`
          )

          return res.status(403).json({
            error: 'insufficient_scope',
            error_description: 'Token must have valid scopes'
          })
        }

        if (
          !scopes.includes('mcp:tools') &&
          (scopes.includes('openid') || scopes.includes('email'))
        ) {
          decoded.scope =
            typeof decoded.scope === 'string'
              ? `${decoded.scope} mcp:tools`
              : [...scopes, 'mcp:tools']
        }
      } else {
        if (decoded.email || decoded.sub || decoded.userId) {
          hasValidScope = true
          decoded.scope = 'mcp:tools read write'
        }
      }

      if (!hasValidScope) {
        res.set(
          'WWW-Authenticate',
          `Bearer realm="${mcpServerUrl}", ` +
            `error="insufficient_scope", ` +
            `error_description="Token must have valid scopes", ` +
            `scope="mcp:tools", ` +
            `resource_metadata="/.well-known/oauth-protected-resource"`
        )

        return res.status(403).json({
          error: 'insufficient_scope',
          error_description: 'Token must have valid scopes'
        })
      }

      req.user = decoded

      next()
    } catch (error) {
      logError('mcp-auth', 'Unexpected error in authentication', {
        error: error.message,
        stack: error.stack
      })

      if (!res.headersSent) {
        return res.status(500).json({
          error: 'server_error',
          error_description: 'Internal server error during authentication'
        })
      }
    }
  }
}

export const handleCors = (): RequestHandler => {
  return (req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*')
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
    res.header(
      'Access-Control-Allow-Headers',
      'Authorization, Content-Type, mcp-session-id'
    )
    res.header('Access-Control-Max-Age', '86400')

    if (req.method === 'OPTIONS') {
      return res.status(204).send()
    }

    next()
  }
}
