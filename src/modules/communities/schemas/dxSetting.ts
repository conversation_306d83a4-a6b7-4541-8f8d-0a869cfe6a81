import Joi from 'joi'
import { DXSettingServices } from '../types/dxSetting'

export const DXSettingRequest = Joi.object({
  communityId: Joi.string().required().example('5f9f1b9b9c9b2b0017b8b1b8'),
  service: Joi.string()
    .valid(...Object.values(DXSettingServices))
    .required()
    .example('funnel'),
  funnel: Joi.object({
    customerApiKey: Joi.string().optional().example('1234567890'),
    externalID: Joi.string().optional().example('1234567890'),
    partnerApiKey: Joi.string().optional().example('1234567890')
  }).optional(),
  entrata: Joi.object({
    subdomain: Joi.string().required().example('subdomain'),
    user: Joi.string().required().example('user'),
    pass: Joi.string().required().example('pass'),
    propertyId: Joi.number().required().example(123456),
    leadSourceId: Joi.number().required().example(123456),
    leasingAgentId: Joi.number().optional().example(123456),
    addressSeparators: Joi.array().required().example(['-', '#'])
  }).optional(),
  realPage: Joi.object({
    user: Joi.string().required().example('user'),
    pass: Joi.string().required().example('pass'),
    siteId: Joi.number().required().example(123456),
    pmcId: Joi.number().optional().example(123456),
    addressSeparators: Joi.array().required().example(['-', '#']),
    useFloorPlanNameMarketing: Joi.boolean().optional().example(true)
  }).optional(),
  yardi: Joi.object({
    url: Joi.string().optional().example('https://example.com'),
    entity: Joi.string().optional().example('entity'),
    license: Joi.string().optional().example('license'),
    user: Joi.string().required().example('user'),
    pass: Joi.string().required().example('pass'),
    propertyId: Joi.string().required().example('123456'),
    serverName: Joi.string().required().example('serverName'),
    database: Joi.string().required().example('database'),
    agentName: Joi.object({
      firstName: Joi.string().required().example('firstName'),
      lastName: Joi.string().required().example('lastName')
    }).optional(),
    transactionSource: Joi.string().optional().example('transactionSource')
  }).optional(),
  anyoneHome: Joi.object({
    propertyId: Joi.string().required().example('123456'),
    apiKey: Joi.string().optional().example('123456'),
    listingContactEmail: Joi.string().required().example('<EMAIL>')
  }).optional(),
  bozzutoMits: Joi.object({
    propertyId: Joi.string().required().example('p123456')
  }).optional(),
  apartmentsDotCom: Joi.object({}).optional(),
  knock: Joi.object({
    propertyId: Joi.string().required().example('123456'),
    companyId: Joi.string().optional().example('123456'),
    version: Joi.number().optional().example(1)
  }).optional(),
  gsDynamics: Joi.object({
    apiKey: Joi.string().required().example('123456'),
    leadSource: Joi.string().required().example('123456'),
    timezone: Joi.string().required().example('America/New_York'),
    url: Joi.string().required().example('https://example.com')
  }).optional(),
  rentCafe: Joi.object({
    apiToken: Joi.string().required().example('123456'),
    propertyId: Joi.string().required().example('123456'),
    url: Joi.string().required().example('https://example.com')
  }).optional(),
  rentCafeV2: Joi.object({
    apiToken: Joi.string().required().example('123456'),
    propertyCode: Joi.string().required().example('123456'),
    propertyId: Joi.number().required().example(123456),
    companyCode: Joi.string().required().example('123456'),
    timezone: Joi.string().required().example('America/New_York'),
    url: Joi.string().optional().example('https://example.com'),
    sendVirtualTours: Joi.boolean().optional().example(true),
    externalID: Joi.string().optional().example('1234567890'),
    marketingUrl: Joi.string().optional().example('https://example.com'),
    source: Joi.string().optional().example('source')
  }).optional(),
  engrain: Joi.object({
    service: Joi.string().required().example('rentCafe'),
    apiKey: Joi.string().optional().example('123456'),
    sightMapUrl: Joi.string().required().example('https://example.com'),
    unitMapUrl: Joi.string().required().example('https://example.com'),
    assetId: Joi.string().required().example('123456'),
    label: Joi.string().required().example('360 Virtual Tour'),
    name: Joi.string().required().example('360 Virtual Tour - Peek'),
    iconType: Joi.string().required().example('360 Virtual'),
    sgtUrlsSent: Joi.boolean().required().example(false),
    sendSgtUrls: Joi.boolean().required().example(false),
    sendFloorplanTours: Joi.boolean().required().example(false)
  }).optional(),
  geokey: Joi.object({
    propertyId: Joi.string().required().example('123456')
  }).optional(),
  latch: Joi.object({
    buildingUuid: Joi.string().required().example('123456')
  }).optional(),
  dataExchange: Joi.object({
    syncProspect: Joi.boolean().required().example(true).optional(),
    syncSgt: Joi.boolean().required().example(true).optional(),
    syncIls: Joi.boolean().required().example(true).optional(),
    syncScheduleATour: Joi.boolean().required().example(true).optional(),
    syncPms: Joi.boolean().required().example(true).optional(),
    syncPIData: Joi.boolean().required().example(true).optional(),
    syncProspectStatus: Joi.boolean().required().example(true).optional(),
    syncPiSummaries: {
      store: Joi.boolean().optional().example(true),
      send: Joi.boolean().optional().example(true)
    },
    syncLease: Joi.boolean().required().example(true).optional()
  }).optional(),
  smartRent: Joi.object({
    propertyId: Joi.string().required().example('123456'),
    apiKey: Joi.string().required(),
    apiSecret: Joi.string().required()
  }).optional(),
  butterflyMX: Joi.object({
    buildings: Joi.array().items(Joi.number()).required().example([123456])
  }).optional(),
  reviewProspect: Joi.boolean().optional().example(true),
  zillow: Joi.object({
    address: Joi.object({
      street: Joi.string().required().example('123 Main St'),
      city: Joi.string().required().example('City'),
      state: Joi.string().required().example('ST'),
      postalCode: Joi.string().required().example('12345'),
      country: Joi.string().optional().example('US')
    }).required()
  }).optional(),
  cortland: Joi.object({
    propertyId: Joi.number().required().example(123456)
  }).optional(),
  resman: Joi.object({
    propertyId: Joi.string().required().example('123456'),
    useMarketingName: Joi.boolean().optional().example(true)
  }).optional(),
  buildingVersion: Joi.string().optional().example('1')
})

export const DXSettingResponse = Joi.object({
  dxSetting: Joi.object({
    communityId: Joi.string().required().example('5f9f1b9b9c9b2b0017b8b1b8'),
    service: Joi.string()
      .valid(...Object.values(DXSettingServices))
      .required()
      .example('funnel'),
    funnel: Joi.object({
      customerApiKey: Joi.string().optional().example('1234567890'),
      partnerApiKey: Joi.string().optional().example('1234567890')
    }).optional()
  })
})

export const DXSettingsResponse = Joi.object({
  dxSettings: Joi.array().items(
    Joi.object({
      communityId: Joi.string().required().example('5f9f1b9b9c9b2b0017b8b1b8'),
      service: Joi.string()
        .valid(...Object.values(DXSettingServices))
        .required()
        .example('funnel'),
      funnel: Joi.object({
        customerApiKey: Joi.string().optional().example('1234567890'),
        partnerApiKey: Joi.string().optional().example('1234567890')
      }).optional()
    })
  )
})

export const DXSettingParamRequest = Joi.object({
  id: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .required(),
  service: Joi.string().required().example('funnel')
})

export const DXSettingQueryRequest = Joi.object({
  _id: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .optional(),
  communityId: Joi.string().optional().example('5f9f1b9b9c9b2b0017b8b1b8'),
  service: Joi.string()
    .valid(...Object.values(DXSettingServices))
    .optional()
    .example('funnel'),
  offset: Joi.number().optional().example(0).default(0),
  limit: Joi.number().optional().example(10).default(10).max(50)
})

export const DXSettingDeletedResponse = Joi.object({
  deleted: Joi.boolean().required().example(true)
})
